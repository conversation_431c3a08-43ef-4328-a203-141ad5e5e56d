#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SuperSpider 运行脚本
使用此脚本启动应用，自动处理包导入问题
"""

import os
import sys

# 确保当前目录在pythonpath中
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

if __name__ == "__main__":
    print("=" * 50)
    print("SuperSpider 启动中...")
    print("请访问 http://127.0.0.1:5000 使用Web界面")
    print("=" * 50)

    # 创建必要的目录
    os.makedirs(os.path.join(current_dir, "logs"), exist_ok=True)
    os.makedirs(os.path.join(current_dir, "data"), exist_ok=True)

    # 导入应用实例
    from backend.main import app

    # 新增：打印路由映射
    print("Registered URL Map:")
    print(app.url_map)
    print("-"*50)

    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True)