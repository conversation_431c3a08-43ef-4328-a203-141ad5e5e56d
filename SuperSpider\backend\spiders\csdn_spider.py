#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN爬虫模块
用于解析CSDN文章内容
"""

import re
import requests
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup

from .base_spider import BaseSpider

# 创建日志记录器
logger = logging.getLogger(__name__)

class CSDNSpider(BaseSpider):
    """CSDN文章爬虫类"""

    def __init__(self):
        """初始化CSDN爬虫"""
        super().__init__("CSDN爬虫")

        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # 设置会话
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def is_valid_url(self, url: str) -> bool:
        """
        验证是否为有效的CSDN文章URL

        Args:
            url: 待验证的URL

        Returns:
            bool: 是否为有效的CSDN URL
        """
        try:
            parsed = urlparse(url)

            # 检查域名
            valid_domains = ['blog.csdn.net', 'www.csdn.net', 'csdn.net']
            if not any(domain in parsed.netloc for domain in valid_domains):
                return False

            # 检查路径格式
            # CSDN文章URL通常格式：https://blog.csdn.net/username/article/details/123456
            if '/article/details/' in parsed.path:
                return True

            return False

        except Exception as e:
            logger.error(f"URL验证失败: {e}")
            return False

    def extract_article_id(self, url: str) -> Optional[str]:
        """
        从URL中提取文章ID

        Args:
            url: CSDN文章URL

        Returns:
            str: 文章ID，如果提取失败返回None
        """
        try:
            # 使用正则表达式提取文章ID
            pattern = r'/article/details/(\d+)'
            match = re.search(pattern, url)

            if match:
                return match.group(1)

            return None

        except Exception as e:
            logger.error(f"提取文章ID失败: {e}")
            return None

    def parse_article(self, url: str) -> Dict[str, Any]:
        """
        解析CSDN文章

        Args:
            url: CSDN文章URL

        Returns:
            Dict: 包含文章信息的字典
        """
        try:
            # 验证URL
            if not self.is_valid_url(url):
                raise ValueError("无效的CSDN文章URL")

            logger.info(f"开始解析CSDN文章: {url}")

            # 发送请求
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章信息
            article_info = self._extract_article_info(soup, url)

            logger.info(f"成功解析CSDN文章: {article_info.get('title', '未知标题')}")

            return {
                'success': True,
                'data': article_info
            }

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"解析文章失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def _extract_article_info(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        从BeautifulSoup对象中提取文章信息

        Args:
            soup: BeautifulSoup解析对象
            url: 原始URL

        Returns:
            Dict: 文章信息字典
        """
        article_info = {
            'url': url,
            'title': '',
            'author': '',
            'content': '',
            'publish_time': '',
            'read_count': 0,
            'tags': []
        }

        try:
            # 提取标题
            title_elem = soup.find('h1', class_='title-article') or soup.find('h1', id='articleTitle')
            if title_elem:
                article_info['title'] = title_elem.get_text().strip()

            # 提取作者
            author_elem = soup.find('a', class_='follow-nickName') or soup.find('div', class_='user-info')
            if author_elem:
                article_info['author'] = author_elem.get_text().strip()

            # 提取发布时间
            time_elem = soup.find('span', class_='time')
            if time_elem:
                article_info['publish_time'] = time_elem.get_text().strip()

            # 提取阅读数
            read_elem = soup.find('span', class_='read-count')
            if read_elem:
                read_text = read_elem.get_text().strip()
                # 提取数字
                read_match = re.search(r'(\d+)', read_text)
                if read_match:
                    article_info['read_count'] = int(read_match.group(1))

            # 提取文章内容
            content_elem = soup.find('div', id='content_views') or soup.find('article', class_='baidu_pl')
            if content_elem:
                # 清理内容：移除脚本、样式等
                for script in content_elem(['script', 'style']):
                    script.decompose()

                article_info['content'] = content_elem.get_text().strip()

            # 提取标签
            tag_elems = soup.find_all('a', class_='tag-link')
            if tag_elems:
                article_info['tags'] = [tag.get_text().strip() for tag in tag_elems]

            # 提取文章ID
            article_id = self.extract_article_id(url)
            if article_id:
                article_info['article_id'] = article_id

        except Exception as e:
            logger.error(f"提取文章信息时出错: {e}")

        return article_info

    def get_article_summary(self, article_info: Dict[str, Any], max_length: int = 200) -> str:
        """
        获取文章摘要

        Args:
            article_info: 文章信息字典
            max_length: 摘要最大长度

        Returns:
            str: 文章摘要
        """
        content = article_info.get('content', '')
        if len(content) <= max_length:
            return content

        # 截取前max_length个字符，并在合适的位置截断
        summary = content[:max_length]

        # 尝试在句号、感叹号或问号处截断
        for punct in ['。', '！', '？', '.', '!', '?']:
            last_punct = summary.rfind(punct)
            if last_punct > max_length * 0.7:  # 如果标点位置不太靠前
                return summary[:last_punct + 1]

        # 如果没有找到合适的标点，就在最后一个空格处截断
        last_space = summary.rfind(' ')
        if last_space > max_length * 0.8:
            return summary[:last_space] + '...'

        return summary + '...'

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行CSDN爬虫任务

        Args:
            params: 任务参数，包含article_url

        Returns:
            Dict: 执行结果
        """
        try:
            article_url = params.get('article_url')
            if not article_url:
                return {
                    'success': False,
                    'message': '缺少文章URL参数',
                    'data': None
                }

            # 解析文章
            article_data = self.parse_article(article_url)

            if article_data:
                return {
                    'success': True,
                    'message': '文章解析成功',
                    'data': article_data
                }
            else:
                return {
                    'success': False,
                    'message': '文章解析失败',
                    'data': None
                }

        except Exception as e:
            logger.error(f"执行CSDN爬虫任务失败: {e}")
            return {
                'success': False,
                'message': f'执行失败: {str(e)}',
                'data': None
            }
