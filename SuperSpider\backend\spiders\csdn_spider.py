#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN爬虫模块
用于解析CSDN文章内容
"""

import re
import time
import random
import requests
import logging
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup

from .base_spider import BaseSpider

# 创建日志记录器
logger = logging.getLogger(__name__)

class CSDNSpider(BaseSpider):
    """CSDN文章爬虫类"""

    def __init__(self):
        """初始化CSDN爬虫"""
        super().__init__("CSDN爬虫")

        # 设置请求头，模拟真实浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-GPC': '1',
        }

        # 设置会话
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 添加反反爬虫措施
        self._setup_anti_detection()

    def _setup_anti_detection(self):
        """设置反反爬虫措施"""
        # 添加随机cookies模拟真实用户
        timestamp = str(int(time.time() * 1000))
        random_id = str(random.randint(100000000, 999999999))

        self.session.cookies.update({
            'uuid_tt_dd': f'10_{timestamp}_{random.randint(10000, 99999)}',
            'dc_session_id': random_id,
            'csrfToken': self._generate_csrf_token(),
            'Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
            'Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac': timestamp,
        })

        # 设置请求间隔，避免请求过于频繁
        self.request_delay = random.uniform(1, 3)

    def _generate_csrf_token(self) -> str:
        """生成CSRF token"""
        chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        return ''.join(random.choice(chars) for _ in range(32))

    def _make_request_with_retry(self, url: str, max_retries: int = 3) -> requests.Response:
        """带重试机制的请求方法"""
        for attempt in range(max_retries):
            try:
                # 添加随机延迟
                if attempt > 0:
                    time.sleep(random.uniform(2, 5))

                # 更新User-Agent（随机化）
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                ]
                self.session.headers['User-Agent'] = random.choice(user_agents)

                response = self.session.get(url, timeout=30)

                # 检查是否被反爬虫拦截
                if self._is_blocked_response(response):
                    logger.warning(f"请求被拦截，尝试重试 (第{attempt + 1}次)")
                    continue

                response.raise_for_status()
                return response

            except requests.RequestException as e:
                logger.warning(f"请求失败 (第{attempt + 1}次): {e}")
                if attempt == max_retries - 1:
                    raise

        raise requests.RequestException("达到最大重试次数")

    def _is_blocked_response(self, response: requests.Response) -> bool:
        """检查响应是否被反爬虫拦截"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True

        # 检查响应内容中的关键词
        content = response.text.lower()
        blocked_keywords = [
            '访问过于频繁',
            '请稍后再试',
            '验证码',
            'captcha',
            '安全验证',
            'security check',
            '访问被拒绝',
            'access denied'
        ]

        return any(keyword in content for keyword in blocked_keywords)

    def is_valid_url(self, url: str) -> bool:
        """
        验证是否为有效的CSDN文章URL

        Args:
            url: 待验证的URL

        Returns:
            bool: 是否为有效的CSDN URL
        """
        try:
            parsed = urlparse(url)

            # 检查域名
            valid_domains = ['blog.csdn.net', 'www.csdn.net', 'csdn.net']
            if not any(domain in parsed.netloc for domain in valid_domains):
                return False

            # 检查路径格式
            # CSDN文章URL通常格式：https://blog.csdn.net/username/article/details/123456
            if '/article/details/' in parsed.path:
                return True

            return False

        except Exception as e:
            logger.error(f"URL验证失败: {e}")
            return False

    def extract_article_id(self, url: str) -> Optional[str]:
        """
        从URL中提取文章ID

        Args:
            url: CSDN文章URL

        Returns:
            str: 文章ID，如果提取失败返回None
        """
        try:
            # 使用正则表达式提取文章ID
            pattern = r'/article/details/(\d+)'
            match = re.search(pattern, url)

            if match:
                return match.group(1)

            return None

        except Exception as e:
            logger.error(f"提取文章ID失败: {e}")
            return None

    def parse_article(self, url: str) -> Dict[str, Any]:
        """
        解析CSDN文章

        Args:
            url: CSDN文章URL

        Returns:
            Dict: 包含文章信息的字典
        """
        try:
            # 验证URL
            if not self.is_valid_url(url):
                raise ValueError("无效的CSDN文章URL")

            logger.info(f"开始解析CSDN文章: {url}")

            # 使用带重试机制的请求方法
            response = self._make_request_with_retry(url)

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章信息
            article_info = self._extract_article_info(soup, url)

            logger.info(f"成功解析CSDN文章: {article_info.get('title', '未知标题')}")

            return {
                'success': True,
                'data': article_info
            }

        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"解析文章失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def _extract_article_info(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        从BeautifulSoup对象中提取文章信息

        Args:
            soup: BeautifulSoup解析对象
            url: 原始URL

        Returns:
            Dict: 文章信息字典
        """
        article_info = {
            'url': url,
            'title': '',
            'author': '',
            'content': '',
            'publish_time': '',
            'read_count': 0,
            'tags': []
        }

        try:
            # 提取标题 - 支持多种选择器
            title_selectors = [
                ('h1', {'class_': 'title-article', 'id': 'articleContentId'}),  # 最新CSDN结构
                ('h1', {'class_': 'title-article'}),
                ('h1', {'id': 'articleTitle'}),
                ('h1', {'id': 'articleContentId'}),  # 新版CSDN
                ('h1', {'class_': 'article-title'}),
                ('.article-header h1', None),
                ('h1', None)  # 通用h1标签
            ]

            for selector, attrs in title_selectors:
                title_elem = None
                if attrs:
                    # 构建查找参数
                    find_attrs = {}
                    if 'class_' in attrs:
                        find_attrs['class_'] = attrs['class_']
                    if 'id' in attrs:
                        find_attrs['id'] = attrs['id']

                    title_elem = soup.find(selector, **find_attrs)
                else:
                    # CSS选择器
                    if selector.startswith('.'):
                        title_elem = soup.select_one(selector)
                    else:
                        title_elem = soup.find(selector)

                if title_elem:
                    article_info['title'] = title_elem.get_text().strip()
                    logger.info(f"找到标题: {article_info['title']}")
                    break

            # 提取作者 - 支持多种选择器
            author_selectors = [
                ('a', {'class_': 'follow-nickName'}),
                ('a', {'class_': 'follow-nickName'}),
                ('.article-info-box .user-info a', None),
                ('.article-bar-top a', None),
                ('.user-profile-head .username', None),
                ('a[href*="/fengbin2005"]', None)  # 根据URL模式匹配
            ]

            for selector, attrs in author_selectors:
                if attrs:
                    author_elem = soup.find(selector, attrs)
                else:
                    if selector.startswith('.') or '[' in selector:
                        author_elem = soup.select_one(selector)
                    else:
                        author_elem = soup.find(selector)

                if author_elem:
                    article_info['author'] = author_elem.get_text().strip()
                    logger.info(f"找到作者: {article_info['author']}")
                    break

            # 提取发布时间
            time_selectors = [
                ('span', {'class_': 'time'}),
                ('.article-info-box .time', None),
                ('.article-bar-top .time', None),
                ('.publish-time', None)
            ]

            for selector, attrs in time_selectors:
                if attrs:
                    time_elem = soup.find(selector, attrs)
                else:
                    time_elem = soup.select_one(selector)

                if time_elem:
                    article_info['publish_time'] = time_elem.get_text().strip()
                    logger.info(f"找到发布时间: {article_info['publish_time']}")
                    break

            # 提取阅读数 - 支持多种选择器
            read_selectors = [
                ('span', {'class_': 'read-count'}),  # 主要选择器
                ('.read-count', None),
                ('.article-info-box .read-count', None),
                ('.article-bar-top .read-count', None),
                ('span[class*="read"]', None),
                ('.read-count-box span', None)
            ]

            for selector, attrs in read_selectors:
                read_elem = None
                if attrs:
                    # 构建查找参数
                    find_attrs = {}
                    if 'class_' in attrs:
                        find_attrs['class_'] = attrs['class_']
                    if 'id' in attrs:
                        find_attrs['id'] = attrs['id']

                    read_elem = soup.find(selector, **find_attrs)
                else:
                    if selector.startswith('.') or '[' in selector:
                        read_elem = soup.select_one(selector)
                    else:
                        read_elem = soup.find(selector)

                if read_elem:
                    read_text = read_elem.get_text().strip()
                    logger.info(f"找到阅读数文本: {read_text}")

                    # 提取数字，支持K、万等单位
                    if '万' in read_text:
                        # 处理万单位
                        read_match = re.search(r'(\d+(?:\.\d+)?)万', read_text)
                        if read_match:
                            article_info['read_count'] = int(float(read_match.group(1)) * 10000)
                    elif 'K' in read_text or 'k' in read_text:
                        # 处理K单位
                        read_match = re.search(r'(\d+(?:\.\d+)?)[Kk]', read_text)
                        if read_match:
                            article_info['read_count'] = int(float(read_match.group(1)) * 1000)
                    else:
                        # 提取纯数字
                        read_match = re.search(r'(\d+)', read_text)
                        if read_match:
                            article_info['read_count'] = int(read_match.group(1))

                    if article_info['read_count'] > 0:
                        logger.info(f"找到阅读数: {article_info['read_count']}")
                        break

            # 提取文章内容 - 支持多种页面结构
            content_elem = self._find_content_element(soup)
            if content_elem:
                # 清理内容：移除脚本、样式、广告等
                self._clean_content_element(content_elem)

                # 获取HTML内容（保留格式）
                article_info['content'] = str(content_elem)

                # 同时保存纯文本内容
                article_info['content_text'] = content_elem.get_text().strip()

            # 提取标签
            tag_elems = soup.find_all('a', class_='tag-link')
            if tag_elems:
                article_info['tags'] = [tag.get_text().strip() for tag in tag_elems]

            # 提取文章ID
            article_id = self.extract_article_id(url)
            if article_id:
                article_info['article_id'] = article_id

        except Exception as e:
            logger.error(f"提取文章信息时出错: {e}")

        return article_info

    def _find_content_element(self, soup: BeautifulSoup):
        """查找文章内容元素，支持多种页面结构"""
        # 按优先级尝试不同的选择器
        selectors = [
            {'id': 'content_views'},  # 新版CSDN
            {'class_': 'article-content'},  # 备用选择器
            {'class_': 'blog-content-box'},  # 另一种结构
            {'class_': 'baidu_pl'},  # 旧版CSDN
            {'tag': 'article'},  # 通用文章标签
            {'class_': 'markdown_views'},  # Markdown格式
        ]

        for selector in selectors:
            if 'id' in selector:
                elem = soup.find('div', id=selector['id'])
            elif 'class_' in selector:
                elem = soup.find('div', class_=selector['class_'])
            elif 'tag' in selector:
                elem = soup.find(selector['tag'])
            else:
                continue

            if elem:
                logger.info(f"找到内容元素: {selector}")
                return elem

        logger.warning("未找到文章内容元素")
        return None

    def _clean_content_element(self, content_elem):
        """清理内容元素，移除不需要的标签和内容"""
        # 移除的标签类型
        remove_tags = ['script', 'style', 'noscript', 'iframe']

        # 移除的CSS类名（广告、推荐等）
        remove_classes = [
            'ad', 'advertisement', 'recommend', 'related',
            'comment', 'share', 'toolbar', 'sidebar',
            'footer', 'header', 'nav', 'menu'
        ]

        # 移除标签
        for tag in remove_tags:
            for elem in content_elem.find_all(tag):
                elem.decompose()

        # 移除包含特定类名的元素
        for class_name in remove_classes:
            for elem in content_elem.find_all(class_=lambda x: x and class_name in ' '.join(x).lower()):
                elem.decompose()

        # 移除空的段落和div
        for elem in content_elem.find_all(['p', 'div']):
            if not elem.get_text().strip():
                elem.decompose()

        # 清理属性，只保留必要的
        for elem in content_elem.find_all():
            # 保留的属性
            keep_attrs = ['href', 'src', 'alt', 'title']
            attrs_to_remove = []

            for attr in elem.attrs:
                if attr not in keep_attrs:
                    attrs_to_remove.append(attr)

            for attr in attrs_to_remove:
                del elem.attrs[attr]

    def get_article_summary(self, article_info: Dict[str, Any], max_length: int = 200) -> str:
        """
        获取文章摘要

        Args:
            article_info: 文章信息字典
            max_length: 摘要最大长度

        Returns:
            str: 文章摘要
        """
        content = article_info.get('content', '')
        if len(content) <= max_length:
            return content

        # 截取前max_length个字符，并在合适的位置截断
        summary = content[:max_length]

        # 尝试在句号、感叹号或问号处截断
        for punct in ['。', '！', '？', '.', '!', '?']:
            last_punct = summary.rfind(punct)
            if last_punct > max_length * 0.7:  # 如果标点位置不太靠前
                return summary[:last_punct + 1]

        # 如果没有找到合适的标点，就在最后一个空格处截断
        last_space = summary.rfind(' ')
        if last_space > max_length * 0.8:
            return summary[:last_space] + '...'

        return summary + '...'

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行CSDN爬虫任务

        Args:
            params: 任务参数，包含article_url

        Returns:
            Dict: 执行结果
        """
        try:
            article_url = params.get('article_url')
            if not article_url:
                return {
                    'success': False,
                    'message': '缺少文章URL参数',
                    'data': None
                }

            # 解析文章
            article_data = self.parse_article(article_url)

            if article_data:
                return {
                    'success': True,
                    'message': '文章解析成功',
                    'data': article_data
                }
            else:
                return {
                    'success': False,
                    'message': '文章解析失败',
                    'data': None
                }

        except Exception as e:
            logger.error(f"执行CSDN爬虫任务失败: {e}")
            return {
                'success': False,
                'message': f'执行失败: {str(e)}',
                'data': None
            }
