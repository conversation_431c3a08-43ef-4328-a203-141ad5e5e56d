#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索历史记录模型
跟踪用户的搜索和解析历史
"""

import datetime
from backend.main import db

class SearchRecord(db.Model):
    """搜索历史记录模型类"""
    __tablename__ = 'search_records'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    platform = db.Column(db.String(50), nullable=False, index=True)  # 平台名称，如 'kuaishou', 'douyin', 'bilibili'
    content_type = db.Column(db.String(50), nullable=False)  # 内容类型，如 'video', 'article'
    content_url = db.Column(db.String(1024), nullable=False)  # 原始内容URL
    title = db.Column(db.String(255))  # 内容标题
    author = db.Column(db.String(100))  # 内容作者
    status = db.Column(db.String(20), default='success', nullable=False)  # 解析状态：success, failed
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, nullable=False)

    # 搜索相关字段
    search_count = db.Column(db.Integer, default=1)  # 搜索次数
    last_searched_at = db.Column(db.DateTime, default=datetime.datetime.now)  # 最后搜索时间
    thumbnail_url = db.Column(db.String(1024))  # 缩略图URL
    duration = db.Column(db.Integer)  # 视频时长（秒）
    video_url = db.Column(db.String(1024))  # 解析出的视频URL
    resolution = db.Column(db.String(20))  # 视频分辨率，如 '720p', '1080p'
    is_favorite = db.Column(db.Boolean, default=False)  # 是否收藏
    notes = db.Column(db.Text)  # 用户笔记

    # 关联到用户模型
    user = db.relationship('User', backref=db.backref('search_records', lazy='dynamic'))

    def __init__(self, user_id, platform, content_type, content_url, **kwargs):
        """初始化搜索记录"""
        self.user_id = user_id
        self.platform = platform
        self.content_type = content_type
        self.content_url = content_url

        for key, value in kwargs.items():
            setattr(self, key, value)

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'platform': self.platform,
            'content_type': self.content_type,
            'content_url': self.content_url,
            'title': self.title,
            'author': self.author,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'search_count': self.search_count,
            'last_searched_at': self.last_searched_at.isoformat() if self.last_searched_at else None,
            'thumbnail_url': self.thumbnail_url,
            'duration': self.duration,
            'video_url': self.video_url,
            'resolution': self.resolution,
            'is_favorite': self.is_favorite,
            'notes': self.notes
        }

    def increment_search_count(self):
        """增加搜索次数"""
        self.search_count += 1
        self.last_searched_at = datetime.datetime.now()

    def toggle_favorite(self):
        """切换收藏状态"""
        self.is_favorite = not self.is_favorite
        return self.is_favorite

    def update_notes(self, notes):
        """更新笔记"""
        self.notes = notes

    def __repr__(self):
        """字符串表示"""
        return f'<SearchRecord {self.id}: {self.platform} - {self.title}>'
