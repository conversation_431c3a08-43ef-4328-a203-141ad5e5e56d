#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索历史记录API模块
提供用户搜索历史记录相关的API
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc

from backend.main import db
from backend.models.search_record import SearchRecord

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
search_api = Blueprint('search_api', __name__, url_prefix='/search')

@search_api.route('/history', methods=['GET'])
@login_required
def get_search_history():
    """
    获取用户搜索历史记录

    查询参数:
        page: 页码，默认1
        per_page: 每页记录数，默认10
        platform: 平台筛选，可选
        content_type: 内容类型筛选，可选
        status: 状态筛选，可选
        record_id: 搜索记录ID，可选，用于查询单个记录

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "records": [
                    {
                        "id": 1,
                        "platform": "kuaishou",
                        "content_type": "video",
                        "title": "视频标题",
                        "author": "作者",
                        "status": "success",
                        "created_at": "2023-01-01T12:00:00"
                    }
                ],
                "total": 100,
                "page": 1,
                "per_page": 10,
                "pages": 10
            }
        }
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        platform = request.args.get('platform')
        content_type = request.args.get('content_type')
        status = request.args.get('status')
        record_id = request.args.get('record_id', type=int)

        # 构建查询
        query = SearchRecord.query.filter_by(user_id=current_user.id)

        # 如果指定了record_id，只查询该记录
        if record_id:
            query = query.filter_by(id=record_id)

        # 应用筛选条件
        if platform:
            query = query.filter_by(platform=platform)
        if content_type:
            query = query.filter_by(content_type=content_type)
        if status:
            query = query.filter_by(status=status)

        # 按创建时间降序排序
        query = query.order_by(desc(SearchRecord.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        records = pagination.items

        # 构建响应数据
        record_list = [record.to_dict() for record in records]

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "records": record_list,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })

    except Exception as e:
        logger.error(f"获取搜索历史记录失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/stats', methods=['GET'])
@login_required
def get_search_stats():
    """
    获取搜索统计信息

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "total_searches": 100,
                "platform_stats": {
                    "kuaishou": 50,
                    "douyin": 30,
                    "bilibili": 20
                },
                "recent_searches": 10,
                "favorite_count": 5,
                "content_type_stats": {
                    "video": 100
                }
            }
        }
    """
    try:
        # 总搜索数
        total_searches = SearchRecord.query.filter_by(user_id=current_user.id).count()

        # 平台统计
        platform_stats = {}
        platforms = db.session.query(SearchRecord.platform, db.func.count(SearchRecord.id))\
            .filter_by(user_id=current_user.id)\
            .group_by(SearchRecord.platform)\
            .all()

        for platform, count in platforms:
            platform_stats[platform] = count

        # 内容类型统计
        content_type_stats = {}
        content_types = db.session.query(SearchRecord.content_type, db.func.count(SearchRecord.id))\
            .filter_by(user_id=current_user.id)\
            .group_by(SearchRecord.content_type)\
            .all()

        for content_type, count in content_types:
            content_type_stats[content_type] = count

        # 最近搜索数（7天内）
        recent_date = datetime.now() - timedelta(days=7)
        recent_searches = SearchRecord.query.filter(
            SearchRecord.user_id == current_user.id,
            SearchRecord.created_at >= recent_date
        ).count()

        # 收藏数量
        favorite_count = SearchRecord.query.filter_by(
            user_id=current_user.id,
            is_favorite=True
        ).count()

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "total_searches": total_searches,
                "platform_stats": platform_stats,
                "content_type_stats": content_type_stats,
                "recent_searches": recent_searches,
                "favorite_count": favorite_count
            }
        })

    except Exception as e:
        logger.error(f"获取搜索统计信息失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/<int:record_id>/favorite', methods=['POST'])
@login_required
def toggle_favorite(record_id):
    """
    切换搜索记录的收藏状态

    路径参数:
        record_id: 搜索记录ID

    响应:
        {
            "success": true,
            "message": "操作成功",
            "data": {
                "is_favorite": true
            }
        }
    """
    try:
        # 查询搜索记录
        record = SearchRecord.query.filter_by(id=record_id, user_id=current_user.id).first()

        if not record:
            return jsonify({
                "success": False,
                "message": "搜索记录不存在或无权访问",
                "data": None
            }), 404

        # 切换收藏状态
        is_favorite = record.toggle_favorite()
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "操作成功",
            "data": {
                "is_favorite": is_favorite
            }
        })

    except Exception as e:
        logger.error(f"切换收藏状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"操作失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/<int:record_id>/notes', methods=['POST'])
@login_required
def update_notes(record_id):
    """
    更新搜索记录的笔记

    路径参数:
        record_id: 搜索记录ID

    请求体:
        {
            "notes": "笔记内容"
        }

    响应:
        {
            "success": true,
            "message": "更新成功",
            "data": {
                "notes": "笔记内容"
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'notes' not in data:
            return jsonify({
                "success": False,
                "message": "请提供笔记内容",
                "data": None
            }), 400

        notes = data['notes']

        # 查询搜索记录
        record = SearchRecord.query.filter_by(id=record_id, user_id=current_user.id).first()

        if not record:
            return jsonify({
                "success": False,
                "message": "搜索记录不存在或无权访问",
                "data": None
            }), 404

        # 更新笔记
        record.update_notes(notes)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "更新成功",
            "data": {
                "notes": record.notes
            }
        })

    except Exception as e:
        logger.error(f"更新笔记失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/record', methods=['POST'])
@login_required
def create_search_record():
    """
    创建搜索记录

    请求体:
        {
            "platform": "kuaishou",
            "content_type": "video",
            "content_url": "https://...",
            "title": "视频标题",
            "author": "作者",
            "video_url": "解析出的视频URL",
            "thumbnail_url": "缩略图URL",
            "duration": 120,
            "resolution": "720p"
        }

    响应:
        {
            "success": true,
            "message": "记录创建成功",
            "data": {
                "record_id": 123
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请提供有效的JSON数据",
                "data": None
            }), 400

        # 验证必需字段
        required_fields = ['platform', 'content_type', 'content_url']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "message": f"缺少必需字段: {field}",
                    "data": None
                }), 400

        # 生成URL哈希值用于重复检查
        from backend.models.search_record import SearchRecord
        content_url_hash = SearchRecord._generate_url_hash(data['content_url'])

        # 检查是否已存在相同的搜索记录（使用URL哈希）
        existing_record = SearchRecord.query.filter_by(
            user_id=current_user.id,
            content_url_hash=content_url_hash
        ).first()

        if existing_record:
            # 如果记录已存在，增加搜索次数
            existing_record.increment_search_count()
            db.session.commit()

            return jsonify({
                "success": True,
                "message": "搜索记录已更新",
                "data": {
                    "record_id": existing_record.id,
                    "search_count": existing_record.search_count
                }
            })

        # 创建新的搜索记录
        record = SearchRecord(
            user_id=current_user.id,
            platform=data['platform'],
            content_type=data['content_type'],
            content_url=data['content_url'],
            title=data.get('title'),
            author=data.get('author'),
            video_url=data.get('video_url'),
            thumbnail_url=data.get('thumbnail_url'),
            duration=data.get('duration'),
            resolution=data.get('resolution')
        )

        try:
            db.session.add(record)
            db.session.commit()

            logger.info(f"用户 {current_user.username} 创建搜索记录: {record.title}")

            return jsonify({
                "success": True,
                "message": "搜索记录创建成功",
                "data": {
                    "record_id": record.id
                }
            })
        except Exception as db_error:
            db.session.rollback()

            # 检查是否是唯一性约束冲突
            if "uq_user_content_url_hash" in str(db_error) or "UNIQUE constraint failed" in str(db_error):
                # 如果是唯一性约束冲突，再次查询现有记录并更新
                existing_record = SearchRecord.query.filter_by(
                    user_id=current_user.id,
                    content_url_hash=content_url_hash
                ).first()

                if existing_record:
                    existing_record.increment_search_count()
                    db.session.commit()

                    return jsonify({
                        "success": True,
                        "message": "搜索记录已更新",
                        "data": {
                            "record_id": existing_record.id,
                            "search_count": existing_record.search_count
                        }
                    })

            # 如果不是唯一性约束冲突，重新抛出异常
            raise db_error

    except Exception as e:
        logger.error(f"创建搜索记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"创建失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/<int:record_id>', methods=['DELETE'])
@login_required
def delete_record(record_id):
    """
    删除搜索记录

    路径参数:
        record_id: 搜索记录ID

    响应:
        {
            "success": true,
            "message": "删除成功",
            "data": null
        }
    """
    try:
        # 查询搜索记录
        record = SearchRecord.query.filter_by(id=record_id, user_id=current_user.id).first()

        if not record:
            return jsonify({
                "success": False,
                "message": "搜索记录不存在或无权访问",
                "data": None
            }), 404

        # 删除记录
        db.session.delete(record)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "删除成功",
            "data": None
        })

    except Exception as e:
        logger.error(f"删除搜索记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/batch-delete', methods=['POST'])
@login_required
def batch_delete_records():
    """
    批量删除搜索记录

    请求体:
        {
            "record_ids": [1, 2, 3]
        }

    响应:
        {
            "success": true,
            "message": "删除成功",
            "data": {
                "deleted_count": 3
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'record_ids' not in data or not isinstance(data['record_ids'], list):
            return jsonify({
                "success": False,
                "message": "请提供有效的搜索记录ID列表",
                "data": None
            }), 400

        record_ids = data['record_ids']

        # 查询属于当前用户的搜索记录
        records = SearchRecord.query.filter(
            SearchRecord.id.in_(record_ids),
            SearchRecord.user_id == current_user.id
        ).all()

        # 删除记录
        deleted_count = 0
        for record in records:
            db.session.delete(record)
            deleted_count += 1

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "删除成功",
            "data": {
                "deleted_count": deleted_count
            }
        })

    except Exception as e:
        logger.error(f"批量删除搜索记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500


@search_api.route('/favorites', methods=['GET'])
@login_required
def get_favorite_records():
    """
    获取收藏的搜索记录

    查询参数:
        page: 页码，默认1
        per_page: 每页记录数，默认10

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "records": [...],
                "total": 5,
                "page": 1,
                "per_page": 10,
                "pages": 1
            }
        }
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询
        query = SearchRecord.query.filter_by(
            user_id=current_user.id,
            is_favorite=True
        ).order_by(desc(SearchRecord.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        records = pagination.items

        # 构建响应数据
        record_list = [record.to_dict() for record in records]

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "records": record_list,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })

    except Exception as e:
        logger.error(f"获取收藏搜索记录失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500