#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
下载历史记录API模块
提供用户下载历史记录相关的API
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from flask import Blueprint, request, jsonify, send_file
from flask_login import login_required, current_user
from sqlalchemy import desc

from backend.main import db
from backend.models.download import Download

# 创建日志记录器
logger = logging.getLogger(__name__)

# 创建蓝图
download_api = Blueprint('download_api', __name__, url_prefix='/download')

@download_api.route('/history', methods=['GET'])
@login_required
def get_download_history():
    """
    获取用户下载历史记录

    查询参数:
        page: 页码，默认1
        per_page: 每页记录数，默认10
        platform: 平台筛选，可选
        content_type: 内容类型筛选，可选
        status: 状态筛选，可选

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "downloads": [
                    {
                        "id": 1,
                        "platform": "kuaishou",
                        "content_type": "video",
                        "title": "视频标题",
                        "author": "作者",
                        "status": "completed",
                        "created_at": "2023-01-01T12:00:00"
                    }
                ],
                "total": 100,
                "page": 1,
                "per_page": 10,
                "pages": 10
            }
        }
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        platform = request.args.get('platform')
        content_type = request.args.get('content_type')
        status = request.args.get('status')

        # 构建查询
        query = Download.query.filter_by(user_id=current_user.id)

        # 应用筛选条件
        if platform:
            query = query.filter_by(platform=platform)
        if content_type:
            query = query.filter_by(content_type=content_type)
        if status:
            query = query.filter_by(status=status)

        # 按创建时间降序排序
        query = query.order_by(desc(Download.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        downloads = pagination.items

        # 构建响应数据
        download_list = [download.to_dict() for download in downloads]

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "downloads": download_list,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })

    except Exception as e:
        logger.error(f"获取下载历史记录失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/file/<int:download_id>', methods=['GET'])
@login_required
def download_file(download_id):
    """
    下载文件

    路径参数:
        download_id: 下载记录ID

    响应:
        文件内容
    """
    try:
        # 查询下载记录
        download = Download.query.filter_by(id=download_id, user_id=current_user.id).first()

        if not download:
            return jsonify({
                "success": False,
                "message": "下载记录不存在或无权访问",
                "data": None
            }), 404

        # 检查文件是否存在
        if not download.file_path or not os.path.exists(download.file_path):
            return jsonify({
                "success": False,
                "message": "文件不存在或已被删除",
                "data": None
            }), 404

        # 更新下载次数
        download.increment_download_count()
        db.session.commit()

        # 发送文件
        return send_file(
            download.file_path,
            as_attachment=True,
            download_name=os.path.basename(download.file_path)
        )

    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"下载失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/stats', methods=['GET'])
@login_required
def get_download_stats():
    """
    获取下载统计信息

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "total_downloads": 100,
                "platform_stats": {
                    "kuaishou": 50,
                    "csdn": 30,
                    "other": 20
                },
                "recent_downloads": 10,
                "favorite_count": 5,
                "content_type_stats": {
                    "video": 70,
                    "article": 30
                }
            }
        }
    """
    try:
        # 总下载数
        total_downloads = Download.query.filter_by(user_id=current_user.id).count()

        # 平台统计
        platform_stats = {}
        platforms = db.session.query(Download.platform, db.func.count(Download.id))\
            .filter_by(user_id=current_user.id)\
            .group_by(Download.platform)\
            .all()

        for platform, count in platforms:
            platform_stats[platform] = count

        # 内容类型统计
        content_type_stats = {}
        content_types = db.session.query(Download.content_type, db.func.count(Download.id))\
            .filter_by(user_id=current_user.id)\
            .group_by(Download.content_type)\
            .all()

        for content_type, count in content_types:
            content_type_stats[content_type] = count

        # 最近下载数（7天内）
        recent_date = datetime.now() - timedelta(days=7)
        recent_downloads = Download.query.filter(
            Download.user_id == current_user.id,
            Download.created_at >= recent_date
        ).count()

        # 收藏数量
        favorite_count = Download.query.filter_by(
            user_id=current_user.id,
            is_favorite=True
        ).count()

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "total_downloads": total_downloads,
                "platform_stats": platform_stats,
                "content_type_stats": content_type_stats,
                "recent_downloads": recent_downloads,
                "favorite_count": favorite_count
            }
        })

    except Exception as e:
        logger.error(f"获取下载统计信息失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/<int:download_id>/favorite', methods=['POST'])
@login_required
def toggle_favorite(download_id):
    """
    切换下载记录的收藏状态

    路径参数:
        download_id: 下载记录ID

    响应:
        {
            "success": true,
            "message": "操作成功",
            "data": {
                "is_favorite": true
            }
        }
    """
    try:
        # 查询下载记录
        download = Download.query.filter_by(id=download_id, user_id=current_user.id).first()

        if not download:
            return jsonify({
                "success": False,
                "message": "下载记录不存在或无权访问",
                "data": None
            }), 404

        # 切换收藏状态
        is_favorite = download.toggle_favorite()
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "操作成功",
            "data": {
                "is_favorite": is_favorite
            }
        })

    except Exception as e:
        logger.error(f"切换收藏状态失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"操作失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/<int:download_id>/notes', methods=['POST'])
@login_required
def update_notes(download_id):
    """
    更新下载记录的笔记

    路径参数:
        download_id: 下载记录ID

    请求体:
        {
            "notes": "笔记内容"
        }

    响应:
        {
            "success": true,
            "message": "更新成功",
            "data": {
                "notes": "笔记内容"
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'notes' not in data:
            return jsonify({
                "success": False,
                "message": "请提供笔记内容",
                "data": None
            }), 400

        notes = data['notes']

        # 查询下载记录
        download = Download.query.filter_by(id=download_id, user_id=current_user.id).first()

        if not download:
            return jsonify({
                "success": False,
                "message": "下载记录不存在或无权访问",
                "data": None
            }), 404

        # 更新笔记
        download.update_notes(notes)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "更新成功",
            "data": {
                "notes": download.notes
            }
        })

    except Exception as e:
        logger.error(f"更新笔记失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"更新失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/<int:download_id>', methods=['DELETE'])
@login_required
def delete_download(download_id):
    """
    删除下载记录

    路径参数:
        download_id: 下载记录ID

    响应:
        {
            "success": true,
            "message": "删除成功",
            "data": null
        }
    """
    try:
        # 查询下载记录
        download = Download.query.filter_by(id=download_id, user_id=current_user.id).first()

        if not download:
            return jsonify({
                "success": False,
                "message": "下载记录不存在或无权访问",
                "data": None
            }), 404

        # 删除记录
        db.session.delete(download)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "删除成功",
            "data": None
        })

    except Exception as e:
        logger.error(f"删除下载记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/batch-delete', methods=['POST'])
@login_required
def batch_delete_downloads():
    """
    批量删除下载记录

    请求体:
        {
            "download_ids": [1, 2, 3]
        }

    响应:
        {
            "success": true,
            "message": "删除成功",
            "data": {
                "deleted_count": 3
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'download_ids' not in data or not isinstance(data['download_ids'], list):
            return jsonify({
                "success": False,
                "message": "请提供有效的下载记录ID列表",
                "data": None
            }), 400

        download_ids = data['download_ids']

        # 查询属于当前用户的下载记录
        downloads = Download.query.filter(
            Download.id.in_(download_ids),
            Download.user_id == current_user.id
        ).all()

        # 删除记录
        deleted_count = 0
        for download in downloads:
            db.session.delete(download)
            deleted_count += 1

        db.session.commit()

        return jsonify({
            "success": True,
            "message": "删除成功",
            "data": {
                "deleted_count": deleted_count
            }
        })

    except Exception as e:
        logger.error(f"批量删除下载记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"删除失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/favorites', methods=['GET'])
@login_required
def get_favorite_downloads():
    """
    获取收藏的下载记录

    查询参数:
        page: 页码，默认1
        per_page: 每页记录数，默认10

    响应:
        {
            "success": true,
            "message": "获取成功",
            "data": {
                "downloads": [...],
                "total": 5,
                "page": 1,
                "per_page": 10,
                "pages": 1
            }
        }
    """
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # 构建查询
        query = Download.query.filter_by(
            user_id=current_user.id,
            is_favorite=True
        )

        # 按创建时间降序排序
        query = query.order_by(desc(Download.created_at))

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        downloads = pagination.items

        # 构建响应数据
        download_list = [download.to_dict() for download in downloads]

        return jsonify({
            "success": True,
            "message": "获取成功",
            "data": {
                "downloads": download_list,
                "total": pagination.total,
                "page": page,
                "per_page": per_page,
                "pages": pagination.pages
            }
        })

    except Exception as e:
        logger.error(f"获取收藏下载记录失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取失败: {str(e)}",
            "data": None
        }), 500

@download_api.route('/record', methods=['POST'])
@login_required
def create_download_record():
    """
    创建下载记录

    请求体:
        {
            "platform": "平台名称",
            "content_type": "内容类型",
            "content_url": "内容URL",
            "title": "标题",
            "author": "作者",
            "file_path": "文件路径",
            "file_size": 文件大小,
            "file_format": "文件格式",
            "resolution": "分辨率",
            "duration": 时长,
            "thumbnail_url": "缩略图URL"
        }

    响应:
        {
            "success": true,
            "message": "创建成功",
            "data": {
                "download_id": 1
            }
        }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "message": "请提供有效的数据",
                "data": None
            }), 400

        # 提取必要字段
        platform = data.get('platform')
        content_type = data.get('content_type')
        content_url = data.get('content_url')
        title = data.get('title')

        if not all([platform, content_type, content_url]):
            return jsonify({
                "success": False,
                "message": "缺少必要字段",
                "data": None
            }), 400

        # 检查是否已存在相同标题和平台的记录
        if title:
            existing_record = Download.query.filter_by(
                user_id=current_user.id,
                platform=platform,
                title=title
            ).first()

            if existing_record:
                # 如果已存在记录，更新最后下载时间和下载次数
                existing_record.increment_download_count()
                db.session.commit()

                return jsonify({
                    "success": True,
                    "message": "记录已存在，已更新下载次数",
                    "data": {
                        "download_id": existing_record.id,
                        "is_new": False
                    }
                })

        # 创建新的下载记录
        download_record = Download(
            user_id=current_user.id,
            platform=platform,
            content_type=content_type,
            content_url=content_url,
            title=title,
            author=data.get('author'),
            file_path=data.get('file_path'),
            file_size=data.get('file_size'),
            status="completed",
            file_format=data.get('file_format'),
            resolution=data.get('resolution'),
            duration=data.get('duration'),
            thumbnail_url=data.get('thumbnail_url'),
            download_count=1,
            last_downloaded_at=datetime.now()
        )

        db.session.add(download_record)
        db.session.commit()

        return jsonify({
            "success": True,
            "message": "创建成功",
            "data": {
                "download_id": download_record.id,
                "is_new": True
            }
        })

    except Exception as e:
        logger.error(f"创建下载记录失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            "success": False,
            "message": f"创建失败: {str(e)}",
            "data": None
        }), 500