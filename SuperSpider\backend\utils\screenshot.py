#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
截图生成工具
使用playwright生成网页长截图
"""

import os
import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime
import tempfile

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# 创建日志记录器
logger = logging.getLogger(__name__)

class ScreenshotGenerator:
    """网页截图生成器"""
    
    def __init__(self):
        """初始化截图生成器"""
        self.browser = None
        self.context = None
        self.page = None
        
        if not PLAYWRIGHT_AVAILABLE:
            logger.warning("Playwright未安装，截图功能将不可用")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright未安装，无法使用截图功能")
        
        self.playwright = await async_playwright().start()
        
        # 启动浏览器（使用chromium）
        self.browser = await self.playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        )
        
        # 创建浏览器上下文
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def capture_full_page(self, url: str, output_path: Optional[str] = None, 
                               wait_time: int = 3, remove_elements: Optional[list] = None) -> str:
        """
        捕获网页完整截图
        
        Args:
            url: 目标网页URL
            output_path: 输出文件路径，如果为None则生成临时文件
            wait_time: 页面加载等待时间（秒）
            remove_elements: 需要移除的元素选择器列表
            
        Returns:
            str: 截图文件路径
        """
        if not self.page:
            raise RuntimeError("截图生成器未初始化")
        
        try:
            logger.info(f"开始截图: {url}")
            
            # 访问页面
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 等待页面完全加载
            await asyncio.sleep(wait_time)
            
            # 移除不需要的元素（如广告、导航栏等）
            if remove_elements:
                for selector in remove_elements:
                    try:
                        await self.page.evaluate(f"""
                            const elements = document.querySelectorAll('{selector}');
                            elements.forEach(el => el.remove());
                        """)
                    except Exception as e:
                        logger.warning(f"移除元素失败 {selector}: {e}")
            
            # 移除CSDN常见的干扰元素
            await self._remove_csdn_distractions()
            
            # 生成输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(tempfile.gettempdir(), f"screenshot_{timestamp}.png")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 截取完整页面
            await self.page.screenshot(
                path=output_path,
                full_page=True,
                type='png'
            )
            
            logger.info(f"截图完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
            raise
    
    async def _remove_csdn_distractions(self):
        """移除CSDN页面的干扰元素"""
        # CSDN常见的干扰元素选择器
        selectors_to_remove = [
            # 顶部导航栏
            '.csdn-header',
            '#csdn_top',
            '.toolbar-container',
            
            # 侧边栏
            '.blog_container_aside',
            '.recommend-box',
            '.aside-box',
            
            # 广告
            '.ad_',
            '[class*="ad-"]',
            '[id*="ad-"]',
            '.baidu_pl',
            
            # 弹窗
            '.modal',
            '.popup',
            '.dialog',
            
            # 底部
            '.csdn-footer',
            '#csdn_footer',
            
            # 其他干扰元素
            '.tool-box',
            '.comment-box',
            '.recommend-item-box',
            '.hide-article-box'
        ]
        
        for selector in selectors_to_remove:
            try:
                await self.page.evaluate(f"""
                    const elements = document.querySelectorAll('{selector}');
                    elements.forEach(el => el.remove());
                """)
            except Exception:
                # 忽略移除失败的元素
                pass
        
        # 移除固定定位的元素
        await self.page.evaluate("""
            const fixedElements = document.querySelectorAll('*');
            fixedElements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.position === 'fixed' || style.position === 'sticky') {
                    el.remove();
                }
            });
        """)
    
    async def capture_article_content(self, url: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        专门用于捕获文章内容的截图
        
        Args:
            url: 文章URL
            output_path: 输出文件路径
            
        Returns:
            Dict: 包含截图路径和文章信息的字典
        """
        if not self.page:
            raise RuntimeError("截图生成器未初始化")
        
        try:
            # 访问页面
            await self.page.goto(url, wait_until='networkidle', timeout=30000)
            
            # 等待内容加载
            await asyncio.sleep(3)
            
            # 提取文章信息
            article_info = await self._extract_article_info()
            
            # 生成截图
            screenshot_path = await self.capture_full_page(url, output_path, wait_time=2)
            
            return {
                'screenshot_path': screenshot_path,
                'article_info': article_info,
                'url': url,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"捕获文章内容失败: {e}")
            raise
    
    async def _extract_article_info(self) -> Dict[str, str]:
        """从页面提取文章信息"""
        try:
            # 提取标题
            title = await self.page.evaluate("""
                const titleEl = document.querySelector('h1.title-article, h1#articleTitle, .article-title h1');
                return titleEl ? titleEl.textContent.trim() : '';
            """)
            
            # 提取作者
            author = await self.page.evaluate("""
                const authorEl = document.querySelector('.follow-nickName, .user-info a, .author-name');
                return authorEl ? authorEl.textContent.trim() : '';
            """)
            
            # 提取发布时间
            publish_time = await self.page.evaluate("""
                const timeEl = document.querySelector('.time, .publish-time, .article-time');
                return timeEl ? timeEl.textContent.trim() : '';
            """)
            
            return {
                'title': title,
                'author': author,
                'publish_time': publish_time
            }
            
        except Exception as e:
            logger.warning(f"提取文章信息失败: {e}")
            return {
                'title': '',
                'author': '',
                'publish_time': ''
            }

def is_screenshot_available() -> bool:
    """检查截图功能是否可用"""
    return PLAYWRIGHT_AVAILABLE

async def generate_screenshot(url: str, output_path: Optional[str] = None) -> str:
    """
    生成网页截图的便捷函数
    
    Args:
        url: 目标URL
        output_path: 输出路径
        
    Returns:
        str: 截图文件路径
    """
    async with ScreenshotGenerator() as generator:
        return await generator.capture_full_page(url, output_path)

async def generate_article_screenshot(url: str, output_path: Optional[str] = None) -> Dict[str, Any]:
    """
    生成文章截图的便捷函数
    
    Args:
        url: 文章URL
        output_path: 输出路径
        
    Returns:
        Dict: 截图结果信息
    """
    async with ScreenshotGenerator() as generator:
        return await generator.capture_article_content(url, output_path)
