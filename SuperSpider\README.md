# SuperSpider

SuperSpider 是一个多平台内容聚合与下载工具，支持多种流行平台内容的获取、格式转换与分享。

## 功能特点

- 支持多平台内容获取（快手、抖音、CSDN等）
- 提供无水印视频直链解析
- 支持文章解析与保存
- 智能错误处理和用户友好提示
- 网络异常自动重试机制
- 用户认证系统
- 下载历史记录管理

## 系统要求

- Python 3.8+
- MySQL 5.7+
- 邮箱账号（用于发送下载内容）

## 安装步骤

### 1. 克隆仓库

```bash
git clone https://github.com/yourusername/superspider.git
cd superspider
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置 MySQL 数据库

确保您已安装并启动 MySQL 服务。

#### 手动创建数据库

```sql
CREATE DATABASE superspider CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 使用初始化脚本

我们提供了一个初始化脚本来创建数据库和管理员用户：

```bash
python scripts/init_db.py --host localhost --port 3306 --user root --database superspider --admin-username admin --admin-email <EMAIL>
```

脚本会提示您输入 MySQL 密码和管理员密码。

### 4. 配置环境变量

您可以通过环境变量配置应用：

```bash
# 数据库配置
export MYSQL_USER=root
export MYSQL_PASSWORD=your_password
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_DATABASE=superspider

# 邮件配置
export QQ_EMAIL=<EMAIL>
export QQ_AUTH_CODE=your_qq_auth_code

# 网站配置
export SECRET_KEY=your_secret_key
export CSDN_COOKIE=your_csdn_cookie
export KUAI_SHOU_COOKIE=your_kuaishou_cookie
```

### 5. 运行应用

```bash
python run.py
```

应用将在 http://127.0.0.1:5000 启动。

## 项目结构

```
SuperSpider/
├── backend/                # 后端代码
│   ├── api/                # API 接口
│   ├── models/             # 数据模型
│   ├── spiders/            # 爬虫模块
│   ├── utils/              # 工具类
│   ├── main.py             # Flask 应用主文件
├── frontend/               # 前端代码
│   ├── static/             # 静态资源
│   ├── templates/          # HTML 模板
├── downloads/              # 下载文件存储目录
├── logs/                   # 日志存储目录
├── scripts/                # 脚本文件
├── requirements.txt        # 依赖包列表
├── run.py                  # 应用启动脚本
```

## API 接口

### 快手视频

- `GET/POST /api/kuaishou/parse` - 解析快手视频
- `GET /api/kuaishou/status` - 检查快手API服务状态

### 抖音视频

- `GET/POST /api/douyin/parse` - 解析抖音视频
- `GET /api/douyin/status` - 检查抖音API服务状态

### CSDN 文章

- `GET/POST /api/csdn/parse` - 解析CSDN文章
- `POST /api/csdn/download` - 下载CSDN文章并发送到邮箱
- `GET /api/csdn/status` - 检查CSDN API服务状态

### 用户认证

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/logout` - 用户退出
- `GET /api/auth/profile` - 获取用户资料
- `GET /api/auth/check-auth` - 检查认证状态

## 贡献指南

欢迎贡献代码，请遵循以下步骤：

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 LICENSE 文件。
