/**
 * SuperSpider 视频播放器控制脚本
 * 处理视频播放器控制和下载功能
 */

// 初始化视频播放器控制
function initVideoControls() {
    // 监听所有视频播放器
    const videoPlayers = document.querySelectorAll('video.video-player');

    videoPlayers.forEach(player => {
        // 为每个视频播放器添加自定义控制菜单
        setupCustomControls(player);

        // 监听视频的下载事件
        monitorVideoDownloads(player);
    });
}

// 监控视频下载
function monitorVideoDownloads(videoPlayer) {
    if (!videoPlayer || initializedPlayers.has(videoPlayer)) {
        return; // 避免重复初始化
    }

    // 标记为已初始化
    initializedPlayers.add(videoPlayer);

    // 获取视频的URL
    const videoUrl = videoPlayer.src;
    if (!videoUrl) return;

    // 创建一个隐藏的下载链接，用于拦截浏览器的下载行为
    const downloadLink = document.createElement('a');
    downloadLink.href = videoUrl;
    downloadLink.download = getVideoFilename(videoPlayer);
    downloadLink.style.display = 'none';
    downloadLink.className = 'video-download-link';
    downloadLink.dataset.videoId = videoUrl;

    // 添加到视频容器
    const container = videoPlayer.closest('.video-preview-container') || videoPlayer.parentElement;
    if (container) {
        container.appendChild(downloadLink);

        // 监听下载链接的点击事件
        downloadLink.addEventListener('click', function(e) {
            // 创建下载记录
            createDownloadRecord(videoPlayer);
        });
    }

    // 监听视频元素本身的右键菜单事件
    // 只记录状态，不自动创建下载记录
    videoPlayer.addEventListener('contextmenu', function(e) {
        // 当用户右键点击视频时，记录这个状态
        console.log('检测到右键点击视频');
        videoPlayer.dataset.rightClicked = 'true';

        // 5秒后重置状态
        setTimeout(() => {
            videoPlayer.dataset.rightClicked = 'false';
        }, 5000);
    });

    // 方法3：拦截浏览器的下载功能
    // 创建一个全局事件监听器来捕获所有下载事件
    if (!window.downloadEventListenerAdded) {
        window.downloadEventListenerAdded = true;

        // 监听所有a标签的点击事件
        document.addEventListener('click', function(e) {
            // 查找最近的a标签
            let target = e.target;
            let aElement = null;

            // 向上查找5层
            for (let i = 0; i < 5; i++) {
                if (!target) break;
                if (target.tagName === 'A' && target.getAttribute('download')) {
                    aElement = target;
                    break;
                }
                target = target.parentElement;
            }

            if (aElement) {
                // 找到了下载链接
                const href = aElement.getAttribute('href');

                // 查找与此URL匹配的视频
                const videos = document.querySelectorAll('video.video-player');
                videos.forEach(video => {
                    if (video.src === href || href.includes(video.src) || video.src.includes(href)) {
                        console.log('检测到视频下载链接点击');
                        createDownloadRecord(video);
                    }
                });
            }
        });
    }
}

// 设置自定义控制菜单
function setupCustomControls(videoPlayer) {
    // 监听视频右键菜单
    videoPlayer.addEventListener('contextmenu', function(e) {
        e.preventDefault(); // 阻止默认右键菜单

        // 创建自定义菜单
        showCustomMenu(e, videoPlayer);
    });

    // 监听视频控制栏的下载按钮点击事件
    videoPlayer.addEventListener('controlschange', function() {
        // 这个事件在大多数浏览器中不存在，但我们可以尝试监听
        console.log('Video controls changed');
    });

    // 监听视频容器的点击事件，检查是否点击了下载按钮
    const videoContainer = videoPlayer.closest('.video-preview-container');
    if (videoContainer) {
        videoContainer.addEventListener('click', function(e) {
            // 检查点击的元素是否是下载按钮或其子元素
            const downloadButton = findDownloadButton(e.target);

            if (downloadButton) {
                // 用户点击了视频的下载按钮
                console.log('Download button clicked in container');

                // 创建下载记录
                createDownloadRecord(videoPlayer);
            }
        });
    }

    // 监听视频元素的菜单按钮点击事件
    if (videoContainer) {
        // 使用MutationObserver监听DOM变化，检测菜单的出现
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // 检查是否添加了菜单元素
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) { // 元素节点
                            // 检查是否是菜单元素
                            if (isVideoControlMenu(node)) {
                                console.log('Video control menu detected');

                                // 查找菜单中的下载按钮
                                const downloadOption = findDownloadOptionInMenu(node);
                                if (downloadOption) {
                                    console.log('Download option found in menu');

                                    // 添加点击事件监听器
                                    downloadOption.addEventListener('click', function() {
                                        console.log('Download option clicked');

                                        // 创建下载记录
                                        createDownloadRecord(videoPlayer);
                                    });
                                }
                            }
                        }
                    }
                }
            });
        });

        // 只观察视频容器，而不是整个文档
        observer.observe(videoContainer, {
            childList: true,
            subtree: true
        });
    }
}

// 检查元素是否是视频控制菜单
function isVideoControlMenu(element) {
    // 检查元素的特征，判断是否是视频控制菜单
    if (element.classList &&
        (element.classList.contains('video-custom-menu') ||
         element.classList.contains('vjs-menu') ||
         element.classList.contains('video-menu'))) {
        return true;
    }

    // 检查元素内容是否包含下载选项
    if (element.textContent &&
        (element.textContent.includes('下载') ||
         element.textContent.includes('Download'))) {
        return true;
    }

    // 检查元素样式是否符合菜单特征
    const style = window.getComputedStyle(element);
    if (style.position === 'absolute' &&
        style.zIndex && parseInt(style.zIndex) > 100 &&
        style.backgroundColor &&
        style.boxShadow) {
        return true;
    }

    return false;
}

// 在菜单中查找下载选项
function findDownloadOptionInMenu(menuElement) {
    // 查找包含"下载"文本的元素
    const downloadOptions = Array.from(menuElement.querySelectorAll('*')).filter(el => {
        return el.textContent &&
              (el.textContent.trim() === '下载' ||
               el.textContent.includes('下载') ||
               el.textContent.trim() === 'Download' ||
               el.textContent.includes('Download'));
    });

    if (downloadOptions.length > 0) {
        return downloadOptions[0];
    }

    // 查找包含下载图标的元素
    const downloadIcons = menuElement.querySelectorAll('.fa-download, .fas.fa-download, .fa.fa-download');
    if (downloadIcons.length > 0) {
        // 返回图标的父元素或祖先元素
        let element = downloadIcons[0];
        for (let i = 0; i < 3; i++) {
            if (element.tagName === 'A' || element.tagName === 'BUTTON' || element.tagName === 'DIV') {
                return element;
            }
            element = element.parentElement;
            if (!element) break;
        }
        return downloadIcons[0].parentElement || downloadIcons[0];
    }

    return null;
}

// 查找下载按钮元素
function findDownloadButton(element) {
    // 检查元素本身或其父元素是否是下载按钮
    let current = element;

    // 向上查找5层父元素
    for (let i = 0; i < 5; i++) {
        if (!current) break;

        // 检查是否是下载按钮
        if (current.classList &&
            (current.classList.contains('download-btn') ||
             current.getAttribute('aria-label') === '下载' ||
             current.textContent.includes('下载'))) {
            return current;
        }

        current = current.parentElement;
    }

    return null;
}

// 检查是否是视频的下载按钮
function isVideoDownloadButton(button, videoPlayer) {
    // 检查按钮是否与视频相关
    // 这里的逻辑可能需要根据实际DOM结构调整
    let current = button;

    // 向上查找10层父元素
    for (let i = 0; i < 10; i++) {
        if (!current) break;

        // 如果找到了视频播放器容器，说明按钮与视频相关
        if (current.classList &&
            (current.classList.contains('video-preview-container') ||
             current.contains(videoPlayer))) {
            return true;
        }

        current = current.parentElement;
    }

    return false;
}

// 显示自定义菜单
function showCustomMenu(event, videoPlayer) {
    // 移除现有的菜单
    removeCustomMenu();

    // 创建菜单容器
    const menu = document.createElement('div');
    menu.className = 'video-custom-menu';
    menu.style.position = 'absolute';
    menu.style.left = `${event.pageX}px`;
    menu.style.top = `${event.pageY}px`;
    menu.style.backgroundColor = '#f8f9fa';
    menu.style.border = '1px solid #ddd';
    menu.style.borderRadius = '4px';
    menu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    menu.style.zIndex = '1000';
    menu.style.padding = '5px 0';

    // 添加下载选项
    const downloadOption = document.createElement('div');
    downloadOption.className = 'menu-option';
    downloadOption.innerHTML = '<i class="fas fa-download"></i> 下载';
    downloadOption.style.padding = '8px 15px';
    downloadOption.style.cursor = 'pointer';
    downloadOption.style.display = 'flex';
    downloadOption.style.alignItems = 'center';
    downloadOption.style.gap = '8px';

    downloadOption.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#e9ecef';
    });

    downloadOption.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
    });

    downloadOption.addEventListener('click', function() {
        // 处理下载操作
        handleDownload(videoPlayer);

        // 关闭菜单
        removeCustomMenu();
    });

    menu.appendChild(downloadOption);

    // 添加播放速度选项
    const speedOption = document.createElement('div');
    speedOption.className = 'menu-option';
    speedOption.innerHTML = '<i class="fas fa-tachometer-alt"></i> 播放速度';
    speedOption.style.padding = '8px 15px';
    speedOption.style.cursor = 'pointer';
    speedOption.style.display = 'flex';
    speedOption.style.alignItems = 'center';
    speedOption.style.gap = '8px';

    speedOption.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#e9ecef';
    });

    speedOption.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
    });

    menu.appendChild(speedOption);

    // 添加画中画选项
    const pipOption = document.createElement('div');
    pipOption.className = 'menu-option';
    pipOption.innerHTML = '<i class="fas fa-external-link-alt"></i> 画中画';
    pipOption.style.padding = '8px 15px';
    pipOption.style.cursor = 'pointer';
    pipOption.style.display = 'flex';
    pipOption.style.alignItems = 'center';
    pipOption.style.gap = '8px';

    pipOption.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#e9ecef';
    });

    pipOption.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
    });

    pipOption.addEventListener('click', function() {
        // 处理画中画操作
        if (document.pictureInPictureEnabled && videoPlayer.readyState > 0) {
            if (document.pictureInPictureElement) {
                document.exitPictureInPicture();
            } else {
                videoPlayer.requestPictureInPicture();
            }
        }

        // 关闭菜单
        removeCustomMenu();
    });

    menu.appendChild(pipOption);

    // 添加到文档
    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    document.addEventListener('click', removeCustomMenu);
    document.addEventListener('contextmenu', removeCustomMenu);
}

// 移除自定义菜单
function removeCustomMenu() {
    const menu = document.querySelector('.video-custom-menu');
    if (menu) {
        menu.remove();
    }

    // 移除事件监听器
    document.removeEventListener('click', removeCustomMenu);
    document.removeEventListener('contextmenu', removeCustomMenu);
}

// 处理下载操作
function handleDownload(videoPlayer) {
    // 获取视频URL
    const videoUrl = videoPlayer.src;

    if (!videoUrl) {
        console.error('无法获取视频URL');
        return;
    }

    // 创建下载记录
    createDownloadRecord(videoPlayer);

    // 触发浏览器下载
    const a = document.createElement('a');
    a.href = videoUrl;
    a.download = getVideoFilename(videoPlayer);
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// 获取视频文件名
function getVideoFilename(videoPlayer) {
    // 尝试从页面获取视频标题
    let title = '';

    // 查找视频标题元素
    const titleElement = document.getElementById('video-title') ||
                         document.getElementById('douyin-video-title') ||
                         document.getElementById('bilibili-video-title');

    if (titleElement && titleElement.textContent) {
        title = titleElement.textContent.trim();
    }

    // 如果找不到标题，使用当前时间作为文件名
    if (!title) {
        const now = new Date();
        title = `video_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
    }

    // 替换不合法的文件名字符
    title = title.replace(/[\\/:*?"<>|]/g, '_');

    return `${title}.mp4`;
}

// 注意：processedDownloads 已移至全局变量

// 创建下载记录（防抖版本）
const createDownloadRecord = debounce(async function(videoPlayer) {
    try {
        // 检查视频播放器是否有效
        if (!videoPlayer || !videoPlayer.src) {
            console.error('无效的视频播放器或视频源');
            return;
        }

        // 获取视频信息
        let videoTitle = '';
        let videoAuthor = '';
        let platform = '';

        // 从视频元素的data属性获取平台信息
        if (videoPlayer.dataset.platform) {
            platform = videoPlayer.dataset.platform;
        }

        // 根据平台获取对应的标题和作者元素
        if (platform === 'kuaishou' || document.getElementById('kuaishou-tool-content')?.classList.contains('active')) {
            platform = 'kuaishou';
            videoTitle = document.getElementById('video-title')?.textContent || '未知标题';
            videoAuthor = document.getElementById('video-author')?.textContent || '未知作者';
        } else if (platform === 'douyin' || document.getElementById('douyin-tool-content')?.classList.contains('active')) {
            platform = 'douyin';
            videoTitle = document.getElementById('douyin-video-title')?.textContent || '未知标题';
            videoAuthor = document.getElementById('douyin-video-author')?.textContent || '未知作者';
        } else if (platform === 'bilibili' || document.getElementById('bilibili-tool-content')?.classList.contains('active')) {
            platform = 'bilibili';
            videoTitle = document.getElementById('bilibili-video-title')?.textContent || '未知标题';
            videoAuthor = document.getElementById('bilibili-video-author')?.textContent || '未知作者';
        } else {
            // 如果无法确定平台，尝试从所有可能的元素中获取信息
            videoTitle = document.getElementById('video-title')?.textContent ||
                         document.getElementById('douyin-video-title')?.textContent ||
                         document.getElementById('bilibili-video-title')?.textContent ||
                         '未知标题';

            videoAuthor = document.getElementById('video-author')?.textContent ||
                          document.getElementById('douyin-video-author')?.textContent ||
                          document.getElementById('bilibili-video-author')?.textContent ||
                          '未知作者';

            // 确定平台
            if (document.getElementById('kuaishou-tool-content')?.classList.contains('active')) {
                platform = 'kuaishou';
            } else if (document.getElementById('douyin-tool-content')?.classList.contains('active')) {
                platform = 'douyin';
            } else if (document.getElementById('bilibili-tool-content')?.classList.contains('active')) {
                platform = 'bilibili';
            } else {
                platform = 'unknown';
            }
        }

        // 创建唯一标识符，使用标题作为唯一标识
        const titleKey = `${platform}:${videoTitle}`;

        console.log('准备创建下载记录:', {
            platform,
            title: videoTitle,
            author: videoAuthor,
            url: videoPlayer.src,
            titleKey: titleKey
        });

        // 使用全局变量来防止重复检查和创建
        const recordKey = `${platform}:${videoTitle}`;

        // 检查是否正在处理中
        if (processedDownloads.has(recordKey)) {
            console.log('该记录正在处理中，跳过重复请求');
            return;
        }

        // 标记为正在处理
        processedDownloads.add(recordKey);

        // 发送请求创建搜索记录
        const response = await fetch('/api/search/record', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                platform: platform,
                content_type: 'video',
                content_url: videoPlayer.src,
                title: videoTitle,
                author: videoAuthor,
                video_url: videoPlayer.src
            })
        });

        const data = await response.json();
        if (data.success) {
            console.log('搜索记录创建成功:', data);

            // 只有在真正创建了新记录时才显示通知
            if (data.data && data.data.record_id) {
                showDownloadNotification(videoTitle, false);
            }
        } else {
            console.error('创建搜索记录失败:', data.message);
        }
    } catch (error) {
        console.error('创建下载记录请求失败:', error);
    } finally {
        // 无论成功还是失败，都要清除处理标记
        // 延迟清除，防止快速重复点击
        setTimeout(() => {
            processedDownloads.delete(recordKey);
        }, 2000);
    }
}, 500); // 500ms 防抖延迟

// 显示下载通知
function showDownloadNotification(title, isExisting = false) {
    // 如果是已存在的记录，不显示通知
    if (isExisting) {
        return;
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'download-notification';
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="notification-content">
            <div class="notification-title">下载记录已创建</div>
            <div class="notification-message">${title}</div>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 设置样式
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = '#fff';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    notification.style.padding = '15px';
    notification.style.display = 'flex';
    notification.style.alignItems = 'center';
    notification.style.gap = '10px';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '300px';
    notification.style.transform = 'translateY(100px)';
    notification.style.opacity = '0';
    notification.style.transition = 'transform 0.3s ease, opacity 0.3s ease';

    // 设置图标样式
    const icon = notification.querySelector('.notification-icon');
    icon.style.color = '#2ecc71'; // 绿色表示新创建
    icon.style.fontSize = '24px';

    // 设置内容样式
    const content = notification.querySelector('.notification-content');
    content.style.flex = '1';

    // 设置标题样式
    const notificationTitle = notification.querySelector('.notification-title');
    notificationTitle.style.fontWeight = 'bold';
    notificationTitle.style.marginBottom = '5px';

    // 设置消息样式
    const message = notification.querySelector('.notification-message');
    message.style.fontSize = '14px';
    message.style.color = '#666';
    message.style.whiteSpace = 'nowrap';
    message.style.overflow = 'hidden';
    message.style.textOverflow = 'ellipsis';

    // 设置关闭按钮样式
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.style.background = 'none';
    closeBtn.style.border = 'none';
    closeBtn.style.cursor = 'pointer';
    closeBtn.style.color = '#999';
    closeBtn.style.fontSize = '16px';

    // 添加到文档
    document.body.appendChild(notification);

    // 显示通知
    setTimeout(() => {
        notification.style.transform = 'translateY(0)';
        notification.style.opacity = '1';
    }, 10);

    // 添加关闭按钮事件
    closeBtn.addEventListener('click', () => {
        notification.style.transform = 'translateY(100px)';
        notification.style.opacity = '0';

        // 移除元素
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    });

    // 自动关闭
    setTimeout(() => {
        notification.style.transform = 'translateY(100px)';
        notification.style.opacity = '0';

        // 移除元素
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// 全局变量，用于跟踪已初始化的视频播放器
const initializedPlayers = new WeakSet();

// 全局变量，用于跟踪已处理的下载
const processedDownloads = new Set();

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 页面加载完成后初始化视频控制
document.addEventListener('DOMContentLoaded', function() {
    // 初始化视频控制
    initVideoControls();

    // 添加全局下载监听
    monitorGlobalDownloads();

    // 监听工具内容区域的变化
    const toolContents = document.getElementById('tool-contents');
    if (toolContents) {
        // 使用MutationObserver监听工具内容区域的变化
        const toolObserver = new MutationObserver(function(mutations) {
            // 当工具内容区域发生变化时，重新初始化视频控制
            initVideoControls();
        });

        // 开始观察工具内容区域
        toolObserver.observe(toolContents, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class']
        });
    }
});

// 监听动态加载的视频
const videoObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            for (let i = 0; i < mutation.addedNodes.length; i++) {
                const node = mutation.addedNodes[i];
                if (node.nodeType === 1) { // 元素节点
                    // 查找新添加的视频播放器
                    const videos = node.querySelectorAll('video.video-player');
                    if (videos.length > 0) {
                        videos.forEach(function(video) {
                            // 检查是否已初始化
                            if (!initializedPlayers.has(video)) {
                                initializedPlayers.add(video);
                                setupCustomControls(video);
                            }
                        });
                    }

                    // 如果添加的是视频容器，也检查其中的视频
                    if (node.classList && node.classList.contains('video-preview-container')) {
                        const containerVideos = node.querySelectorAll('video.video-player');
                        if (containerVideos.length > 0) {
                            containerVideos.forEach(function(video) {
                                if (!initializedPlayers.has(video)) {
                                    initializedPlayers.add(video);
                                    setupCustomControls(video);
                                }
                            });
                        }
                    }
                }
            }
        }
    });
});

// 开始观察视频容器区域
const videoContainers = document.querySelectorAll('.video-preview-container, #kuaishou-result, #douyin-result, #bilibili-result');
videoContainers.forEach(function(container) {
    if (container) {
        videoObserver.observe(container, {
            childList: true,
            subtree: true
        });
    }
});

// 监控全局下载行为
function monitorGlobalDownloads() {
    // 我们只需要监听视频右下角的三点菜单中的下载选项
    // 其他所有的监听都可能导致误触发

    // 这部分主要通过MutationObserver在setupCustomControls中实现
    // 在那里我们已经监听了菜单的出现，并为下载选项添加了点击事件

    // 为了确保只有在点击三点菜单中的下载选项时才创建记录，
    // 我们不再添加全局的点击事件监听器

    console.log('已初始化下载监控，只有点击视频右下角三点菜单中的下载选项才会创建记录');

    // 清除可能存在的全局事件监听器
    if (window.globalClickHandler) {
        document.removeEventListener('click', window.globalClickHandler);
    }
}
