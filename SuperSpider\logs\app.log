2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:50:45,859 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:45,860 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:46,353 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:47,063 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:47,069 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:47,103 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:47,116 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:47,121 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:47,124 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:47,127 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:47,132 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:47,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:47,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 20:50:47,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 20:50:47,233 - werkzeug - INFO -  * Restarting with stat
2025-05-27 20:50:48,289 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:48,291 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:48,801 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:49,431 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:49,436 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:49,462 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:49,474 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:49,489 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:49,500 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:49,504 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:49,510 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:49,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:49,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 20:50:49,657 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 20:51:42,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:42,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,647 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,675 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:42,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,713 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:42,714 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:45,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,262 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,274 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:45,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,394 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:45,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:45,571 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:46,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:56:46,479 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:56:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:01:46,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:01:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:02:08,756 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:09,363 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:10,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:10,400 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:10,935 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:11,615 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:11,619 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:11,640 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:11,649 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:11,654 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:11,669 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:11,672 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:11,679 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:11,685 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:11,729 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:11,788 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:11,823 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:25,063 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:25,243 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:26,004 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:26,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:26,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:26,413 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:26,861 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:26,865 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:26,881 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:26,934 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:26,943 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:26,957 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:26,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:26,965 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:26,969 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:26,995 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:27,016 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:27,029 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:50,371 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 21:02:50,545 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:51,261 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:51,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:51,669 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:52,131 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:52,135 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:52,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:52,192 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:52,206 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:52,211 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:52,215 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:52,218 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:52,223 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:52,246 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:52,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:52,286 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:06:22,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET / HTTP/1.1" 200 -
2025-05-27 21:06:22,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 21:06:22,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,747 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:22,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:23,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,266 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:06:24,207 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:24,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:29,712 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:06:30,362 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:06:34,567 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:06:34,569 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:34] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:06:43,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:43,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,419 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:08:04,841 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:05,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:06,115 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:06,116 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:06,117 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:06,573 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:07,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:07,155 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:07,219 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:07,228 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:07,244 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:07,255 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:07,259 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:07,263 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:07,268 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:07,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:07,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:07,357 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:08:18,583 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:18,946 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:19,684 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:19,686 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:20,063 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:20,461 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:20,464 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:20,475 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:20,482 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:20,487 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:20,499 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:20,503 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:20,505 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:20,507 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:20,527 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:20,546 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:20,558 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:11:24,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:11:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:16:24,265 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:16:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,008 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 21:19:55,778 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:05,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:05,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:11,609 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:11,654 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:21:24,255 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:21:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:26:24,309 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:26:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:31:24,244 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:31:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:04,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET / HTTP/1.1" 200 -
2025-05-27 21:32:04,680 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,710 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,774 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,788 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,808 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 21:32:04,827 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,973 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,001 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,497 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,509 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,610 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:32:06,547 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:06,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:10,762 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:32:11,323 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 21:32:15,800 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 21:32:15,802 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:15] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:32:19,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:19,942 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:28,851 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:32:33,053 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:32:33,054 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:33] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:32:36,042 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:36,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:36:10,855 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:36:11,646 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:16,204 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:16,207 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:16,790 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:17,577 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:17,584 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:17,622 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:17,633 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:17,638 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:17,640 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:17,644 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:17,648 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:17,675 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:17,721 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:40:17,722 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:40:17,724 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:18,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:18,435 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:18,436 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:18,950 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:19,450 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:19,453 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:19,467 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:19,472 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:19,477 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:19,479 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:19,482 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:19,485 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:19,505 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:19,524 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:40:19,546 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:41:05,247 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:05,249 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:05,249 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:05,628 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:06,039 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:06,043 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:06,062 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:06,067 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:06,073 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:06,074 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:06,079 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:06,081 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:06,101 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:06,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:41:06,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:41:06,137 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:41:06,825 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:06,827 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:06,827 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:07,233 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:07,664 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:07,667 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:07,680 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:07,686 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:07,691 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:07,693 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:07,696 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:07,698 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:07,714 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:07,735 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:41:07,748 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:06,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:42:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:42:13,566 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:13,842 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:14,537 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:14,540 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:14,540 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:14,894 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:15,327 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:15,331 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:15,347 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:15,353 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:15,361 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:15,367 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:15,371 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:15,374 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:15,394 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:15,419 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:15,478 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:34,744 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:34,899 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:35,749 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:35,751 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:36,113 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:36,515 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:36,518 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:36,531 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:36,536 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:36,539 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:36,546 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:36,549 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:36,551 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:36,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:36,585 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:36,598 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:50,814 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:50,977 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:51,820 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:51,822 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:51,822 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:52,199 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:52,681 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:52,685 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:52,699 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:52,706 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:52,710 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:52,715 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:52,718 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:52,722 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:52,737 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:52,761 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:52,781 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:06,987 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:43:07,183 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:43:07,960 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:07,962 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:08,379 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:08,850 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:08,854 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:08,870 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:08,880 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:08,887 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:08,897 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:08,909 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:08,949 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:09,027 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:09,131 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:43:09,144 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:58,520 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:58,522 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:58,522 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:58,928 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:59,387 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:59,392 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:59,407 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:59,412 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:59,416 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:59,421 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:59,424 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:59,428 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:59,450 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:59,502 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:43:59,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:43:59,505 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:00,152 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:00,155 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:00,532 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:00,954 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:00,957 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:00,972 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:00,977 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:00,980 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:00,983 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:00,987 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:00,990 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:01,008 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:01,029 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:01,045 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:11,951 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:11,953 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:12,371 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:12,790 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:12,794 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:12,806 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:12,813 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:12,816 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:12,820 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:12,822 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:12,824 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:12,842 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:12,886 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:44:12,887 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:44:12,889 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:13,549 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:13,550 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:13,908 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:14,301 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:14,305 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:14,317 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:14,324 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:14,330 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:14,332 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:14,335 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:14,337 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:14,351 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:14,374 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:14,389 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:32,666 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "GET / HTTP/1.1" 200 -
2025-05-27 21:44:32,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,931 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,945 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,958 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,963 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,986 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,992 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:33,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,282 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:44:35,724 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:35,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:41,186 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:44:41,574 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:44:45,354 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:44:45,357 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:45] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:44:49,005 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:49,031 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:45:19,105 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:19,106 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:19,591 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:20,133 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:20,137 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:20,151 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:20,155 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:20,159 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:20,162 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:20,165 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:20,168 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:20,184 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:20,243 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:45:20,244 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:45:20,248 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:20,870 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:20,872 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:21,219 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:21,618 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:21,621 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:21,638 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:21,644 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:21,650 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:21,652 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:21,655 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:21,657 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:21,681 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:21,705 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:21,722 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:45:46,086 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\run.py', reloading
2025-05-27 21:45:46,288 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:47,043 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:47,045 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:47,045 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:47,499 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:48,079 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:48,084 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:48,102 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:48,110 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:48,116 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:48,121 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:48,125 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:48,128 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:48,152 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:48,178 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:48,191 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:08,937 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:08,939 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:08,939 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:09,433 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:47:09,931 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:47:09,934 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:47:09,949 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:47:09,954 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:47:09,959 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:47:09,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:47:09,964 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:47:09,966 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:47:09,985 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:10,042 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:47:10,043 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:47:10,046 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:47:10,756 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:10,759 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:10,759 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:11,812 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:11,830 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:47:11,848 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:29,325 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "GET / HTTP/1.1" 200 -
2025-05-27 21:47:29,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,651 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,670 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,716 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,739 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,741 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,751 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,756 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:30,145 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,162 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,672 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:37,800 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:37,802 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:38,331 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:52:38,955 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:52:38,959 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:52:38,978 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:52:38,985 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:52:38,989 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:52:38,992 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:52:38,999 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:52:39,001 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:52:39,079 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:39,093 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:52:39,094 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:52:39,098 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:52:39,907 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:39,911 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:39,912 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:40,810 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:40,825 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:52:40,840 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:52:46,837 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "GET / HTTP/1.1" 200 -
2025-05-27 21:52:46,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,938 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,015 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,056 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,067 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,070 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,071 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:52:47,304 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,364 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,809 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:49,810 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:52:50,435 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:52:54,725 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:52:54,789 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,793 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:05,594 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:53:09,315 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:53:09,341 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:14,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,269 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,484 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:53:14,496 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:08,605 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:08,607 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:08,607 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:09,071 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:54:09,695 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:54:09,701 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:54:09,723 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:54:09,731 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:54:09,739 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:54:09,746 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:54:09,752 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:54:09,758 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:54:09,790 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:09,855 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:54:09,856 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:54:09,858 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:54:10,551 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:10,553 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:10,553 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:11,403 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:11,422 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:54:11,440 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:54:14,245 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 21:54:14,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,298 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,308 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,314 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,320 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,370 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,371 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,385 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,400 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,403 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,417 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,711 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:14,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:15,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,213 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,258 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,572 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:27,270 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-05-27 21:54:27,270 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-05-27 21:54:27,703 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-05-27 21:54:28,095 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,096 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-05-27 21:54:28,113 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-05-27 21:54:28,127 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,150 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,153 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:54:28,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:32,598 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,629 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:32,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:35,943 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:35] "DELETE /api/search/2 HTTP/1.1" 200 -
2025-05-27 21:54:36,264 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:36,296 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:18,718 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:18,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:18,751 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:18,754 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:18,755 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:19,888 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:58:21,135 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:58:21,140 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:58:21,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:58:21,188 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:58:21,194 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:58:21,198 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:58:21,203 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:58:21,208 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:58:21,233 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:21,293 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:58:21,294 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:58:21,297 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:58:23,012 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:23,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:24,418 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:24,441 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:58:24,511 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:58:30,505 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:32,178 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,179 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:32,235 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:34,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,000 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,064 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:35,103 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:44,324 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:44,680 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:44,704 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,706 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:46,910 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:47,023 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:47] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:50,586 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "DELETE /api/search/5 HTTP/1.1" 200 -
2025-05-27 21:58:50,905 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:50,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:59:15,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:59:16,547 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:59:16,896 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:59:16,918 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,919 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:01:25,789 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:01:26,190 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:26,994 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:26,996 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:28,182 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:28,206 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:28,221 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:01:48,540 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:01:48,902 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:49,829 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:49,830 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:50,709 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:50,731 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:50,751 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:04:15,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:04:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:09:15,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:09:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:14:15,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:14:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:19:15,248 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:19:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:24:15,267 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:24:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:30:08,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:30:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:35:08,250 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:35:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:40:08,235 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:40:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:45:08,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:45:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:09,267 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:09,269 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:09,270 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:09,826 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:10,880 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:10,884 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:10,912 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:10,919 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:10,922 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:10,925 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:10,930 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:10,933 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:10,958 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:10,999 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:10,999 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:11,002 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:16,330 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:16,332 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:16,332 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:16,692 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:17,275 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:17,280 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:17,299 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:17,309 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:17,314 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:17,317 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:17,320 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:17,325 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:17,348 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:17,398 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:17,399 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:17,401 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:18,035 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:18,036 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:18,037 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:18,913 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:18,929 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:48:18,949 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:48:28,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 22:48:28,333 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,345 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,374 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,461 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,482 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,527 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 22:48:28,579 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 22:48:28,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 22:48:28,832 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:48:28,848 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:28] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:48:29,324 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:29,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:29,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:29] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 22:48:32,322 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 22:48:32,820 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 22:48:36,687 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 22:48:36,696 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:36] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 22:48:37,457 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 22:48:37,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:37] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:48:44,678 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 22:48:46,134 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:48:46,135 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 22:48:46,528 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:48:46,543 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:48:46,738 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:48:46,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:48:46,790 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:48:46,791 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:48:46] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:49:49,526 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:49:49,555 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:49:49,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:49:49,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:49:49] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:53:30,381 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:53:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:54:19,570 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:54:20,290 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:54:21,440 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:54:21,442 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:54:21,443 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:54:21,450 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:54:21,450 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:54:22,924 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:54:22,954 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:54:22,976 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:55:44,504 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:55:44,958 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:55:46,197 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:55:46,271 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:55:46,277 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:55:46,282 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:55:46,282 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:55:47,714 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:55:47,758 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:55:47,887 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:56:29,626 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:56:30,024 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:56:31,387 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:56:31,388 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:56:31,388 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:56:31,389 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:56:31,389 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:56:32,619 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:56:32,651 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:56:32,668 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:56:43,882 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\migrate_search_records.py', reloading
2025-05-27 22:56:44,154 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:56:45,168 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:56:45,170 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:56:45,170 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:56:46,186 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:56:46,204 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:56:46,220 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:13,716 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:14,105 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:15,258 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:15,264 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:15,267 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:15,268 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:15,269 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:16,596 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:16,612 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:16,625 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:34,028 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:34,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:35,231 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:35,233 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:35,233 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:36,409 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:36,441 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:36,466 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:57:49,784 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:57:50,061 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:57:51,017 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:57:51,018 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:57:51,019 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:57:52,035 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:57:52,053 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:57:52,066 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:01,229 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:58:01,470 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:58:02,158 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:58:02,160 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:58:02,160 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:58:03,064 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:58:03,080 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:58:03,093 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:16,330 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:58:16,567 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:58:17,187 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:58:17,189 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:58:17,190 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:58:17,973 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:58:17,988 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:58:18,000 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:58:30,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:58:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:32,678 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 22:59:32,766 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,771 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,785 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,786 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,817 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,831 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,851 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,852 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,855 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,862 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,864 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 22:59:32,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:32] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:59:33,014 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 22:59:33,491 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:33,506 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:59:33,542 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:33] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 22:59:34,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:34,368 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:34,402 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:34,422 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:34] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:41,877 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:41] "DELETE /api/search/8 HTTP/1.1" 200 -
2025-05-27 22:59:42,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:42] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:42,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:42] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:48,839 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 22:59:50,058 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:59:50,058 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:50] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 22:59:50,590 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 22:59:50,592 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:50] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:59:55,050 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:55,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 22:59:55,148 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 22:59:55,163 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:59:55] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:04,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:04] "DELETE /api/search/9 HTTP/1.1" 200 -
2025-05-27 23:00:05,166 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:05] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:05,184 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:05] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:45,515 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:00:45,516 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:00:45,517 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:00:45,902 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:00:46,441 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:00:46,446 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:00:46,470 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:00:46,479 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:00:46,488 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:00:46,492 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:00:46,499 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:00:46,503 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:00:46,529 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:00:46,576 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:00:46,576 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:00:46,578 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:00:47,214 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:00:47,215 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:00:47,216 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:00:48,050 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:00:48,067 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:00:48,085 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:00:49,677 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "GET / HTTP/1.1" 200 -
2025-05-27 23:00:49,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,896 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,914 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,930 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,943 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,951 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,971 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,974 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,987 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:49,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:49] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:00:50,321 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:00:50,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:00:50,872 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:00:50,879 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:00:50,922 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:50] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:00:52,321 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:52,332 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:00:52,406 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:52,441 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:00:52] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:00:59,735 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 23:01:00,233 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 23:01:03,889 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 23:01:03,890 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:03] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 23:01:04,488 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 23:01:04,499 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:04] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:01:09,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:09,070 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:09,134 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:09,159 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:09] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:13,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "DELETE /api/search/10 HTTP/1.1" 200 -
2025-05-27 23:01:13,462 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:01:13,527 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:01:13] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:01:24,806 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:01:24,808 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:01:24,808 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:01:25,770 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:01:26,362 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:01:26,366 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:01:26,390 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:01:26,396 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:01:26,403 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:01:26,407 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:01:26,411 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:01:26,413 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:01:26,440 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:01:26,494 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:01:26,495 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:01:26,497 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:01:27,413 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:01:27,414 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:01:27,420 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:01:28,558 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:01:28,578 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:01:28,629 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:04,312 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:04,875 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:05,085 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:06,709 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:07,358 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:07,359 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:07,360 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:08,157 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:08,159 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:08,159 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:09,105 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:09,135 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:09,169 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:09,441 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:09,466 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:09,480 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:21,703 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:21,971 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:22,402 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:23,036 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:23,386 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:23,389 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:23,390 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:23,391 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:23,391 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:24,011 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:24,013 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:24,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:24,570 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:24,602 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:24,620 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:25,152 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:25,170 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:25,186 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:30,311 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:30,657 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:30,744 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 23:02:31,042 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:31,728 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:31,731 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:31,731 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:31,986 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:31,988 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:31,988 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:31,989 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:31,989 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:33,085 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:33,106 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:33,123 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:33,313 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:33,330 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:33,343 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:46,355 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:46,543 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:46,635 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:46,812 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:47,365 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:47,367 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:47,367 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:47,368 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:47,368 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:47,606 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:47,607 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:47,607 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:47,608 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:47,608 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:02:48,298 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:48,321 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:48,336 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:48,572 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:02:48,595 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:02:48,607 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:02:58,815 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:59,096 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:59,545 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 23:02:59,811 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:02:59,943 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:02:59,945 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:02:59,945 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:02:59,946 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:02:59,946 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:03:00,842 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:03:00,843 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:03:00,843 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:03:00,844 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:03:00,844 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:03:01,249 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:03:01,280 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:03:01,302 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:03:01,976 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:03:01,995 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:03:02,010 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:05:51,423 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:05:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:10:51,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:10:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:11:50,035 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:11:50,036 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:11:50,036 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:11:50,516 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 23:11:51,283 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 23:11:51,288 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 23:11:51,316 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 23:11:51,329 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 23:11:51,335 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 23:11:51,344 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 23:11:51,351 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 23:11:51,365 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 23:11:51,438 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:11:51,662 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 23:11:51,662 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 23:11:51,665 - werkzeug - INFO -  * Restarting with stat
2025-05-27 23:11:52,584 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 23:11:52,585 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 23:11:52,586 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 23:11:54,129 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 23:11:54,147 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 23:11:54,165 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 23:12:01,648 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "GET / HTTP/1.1" 200 -
2025-05-27 23:12:01,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,761 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,779 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,782 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,783 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,817 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,825 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,881 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,884 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:01,916 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:01] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:12:02,058 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:12:02,081 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:12:02,544 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:12:02,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:12:02,612 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:12:06,075 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:07,580 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:07,580 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:07] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:08,128 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:08] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:35,874 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:35,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:36,024 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:36,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:41,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:41,767 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:41,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:41,854 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:41] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:44,639 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:44] "DELETE /api/search/4 HTTP/1.1" 200 -
2025-05-27 23:12:44,985 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:44] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:45,051 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:45] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:46,533 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:46,880 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:46,913 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:46] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:47,516 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:47,522 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:47] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:51,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:51,949 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:51,953 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:51,971 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:51] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:54,497 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:12:54,948 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:12:54,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:54] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:12:55,470 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:55] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:12:59,742 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:59,764 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:12:59,781 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:12:59,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:12:59] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:22,660 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 23:13:23,087 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 23:13:23,088 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:23] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 23:13:23,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:23] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:13:25,137 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET / HTTP/1.1" 200 -
2025-05-27 23:13:25,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,214 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,240 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,383 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,408 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,412 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,450 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,455 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 23:13:25,504 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:13:25,526 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 23:13:25,981 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:25,998 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:25] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:26,023 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:26] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:13:29,572 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 23:13:30,073 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 23:13:33,339 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 23:13:33,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:33] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 23:13:33,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:33] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 23:13:38,192 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:13:38,242 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 23:13:38,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:38,331 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:38] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 23:13:46,562 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:46] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 23:13:47,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:13:47,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:13:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 23:14:12,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:14:12] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 23:18:26,246 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:18:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:23:26,228 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:23:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:28:26,220 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:28:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:33:26,222 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:33:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:38:26,224 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:38:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 23:43:26,223 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 23:43:26] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:32:45,318 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:32:45,320 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:32:45,320 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:32:45,798 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:32:46,547 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:32:46,552 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:32:46,574 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:32:46,581 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:32:46,585 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:32:46,588 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:32:46,591 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:32:46,595 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:32:46,618 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:32:46,682 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:32:46,683 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:32:46,685 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:32:47,372 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:32:47,374 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:32:47,375 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:32:47,376 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:32:47,376 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:32:48,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:32:48,317 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:32:48,331 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:33:06,693 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "GET / HTTP/1.1" 200 -
2025-05-28 15:33:06,915 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:06,934 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:06,999 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:06] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,014 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,016 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,021 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,039 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,065 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,078 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 15:33:07,090 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,118 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,125 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:07,379 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:07,401 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:07,873 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:07,883 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:07,954 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:07] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:33:14,817 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:14] "GET / HTTP/1.1" 200 -
2025-05-28 15:33:15,050 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,057 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,069 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,090 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,125 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,183 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,234 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,258 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,372 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,473 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,474 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:15,693 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:15,707 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:15] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:16,183 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:16,191 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:16,222 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:16] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:33:35,129 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:33:35,148 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,148 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,162 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,163 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,187 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,229 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,242 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,264 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,299 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,342 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,397 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,417 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:33:35,460 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:35,473 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:33:35,936 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:35,947 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:33:35,979 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:33:35] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:35:24,005 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:35:24,225 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:35:25,169 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:35:25,171 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:35:25,172 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:35:26,456 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:35:26,484 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:35:26,495 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:35:52,865 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:35:53,206 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:35:54,003 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:35:54,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:35:54,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:35:54,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:35:54,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:35:55,110 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:35:55,144 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:35:55,176 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:36:48,017 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:36:48,262 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:36:49,082 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:36:49,084 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:36:49,084 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:36:50,215 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:36:50,233 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:36:50,244 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:37:11,595 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 15:37:11,953 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:37:12,991 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:37:12,996 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:37:12,996 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:37:12,997 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:37:12,997 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:37:14,301 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:37:14,330 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:37:14,344 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:38:25,653 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\mailer.py', reloading
2025-05-28 15:38:25,930 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:38:26,979 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:38:26,980 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:38:26,981 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:38:28,315 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:38:28,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:38:28,347 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:38:36,032 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:38:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:02,013 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:39:02,014 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:39:02,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:39:02,397 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:39:02,788 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:39:02,792 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:39:02,977 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:39:02,981 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:39:02,985 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:39:02,989 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:39:02,993 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:39:02,996 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:39:03,015 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:39:03,077 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:39:03,078 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:39:03,080 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:39:03,747 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:39:03,748 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:39:03,748 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:39:03,749 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:39:03,749 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:39:04,873 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:39:04,901 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:39:04,933 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:39:08,732 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:08] "GET / HTTP/1.1" 200 -
2025-05-28 15:39:09,143 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,173 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,200 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,209 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,260 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,286 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,339 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,345 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,397 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,415 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,421 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:09,832 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:09,849 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:09] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:10,180 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:10,197 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:10,276 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:10] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:39:19,428 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:39:19,454 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,464 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,468 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,486 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,502 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,527 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,542 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,548 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,561 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,577 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,594 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,657 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:39:19,759 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:19,777 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:19] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:39:20,169 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:20,175 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:39:20,201 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:39:20] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:45:08,276 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:45:08,278 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:45:08,279 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:45:08,279 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:45:08,279 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:45:08,700 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:45:09,294 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:45:09,299 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:45:09,504 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:45:09,510 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:45:09,514 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:45:09,516 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:45:09,519 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:45:09,523 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:45:09,549 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:45:09,609 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:45:09,610 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:45:09,613 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:45:10,263 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:45:10,264 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:45:10,264 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:45:11,453 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:45:11,473 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:45:11,485 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:45:16,616 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-28 15:45:17,158 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-28 15:45:23,637 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-28 15:45:23,640 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:23] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-28 15:45:24,287 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:24] "POST /api/search/record HTTP/1.1" 200 -
2025-05-28 15:45:31,369 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:45:31,403 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,439 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,440 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,445 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,453 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,462 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,469 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,474 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,488 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 15:45:31,493 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,497 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,505 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:45:31,541 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:45:31,556 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:31] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:45:32,036 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:45:32,043 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:45:32,078 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:45:32] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:46:57,596 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-05-28 15:46:57,598 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-05-28 15:46:57,600 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:46:57] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-05-28 15:47:01,964 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:01] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:47:01,991 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,009 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,028 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,038 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,061 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,073 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,075 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,137 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,160 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,177 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,182 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:47:02,214 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:47:02,234 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:47:02,700 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:47:02,709 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:47:02,731 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:02] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:47:07,174 - backend.api.csdn_api - ERROR - 解析文章失败: Can't instantiate abstract class CSDNSpider with abstract method execute
2025-05-28 15:47:07,175 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 98, in parse_article
    spider = CSDNSpider()
             ^^^^^^^^^^^^
TypeError: Can't instantiate abstract class CSDNSpider with abstract method execute

2025-05-28 15:47:07,176 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:47:07] "[35m[1mPOST /api/csdn/parse HTTP/1.1[0m" 500 -
2025-05-28 15:48:18,290 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 15:48:18,547 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:48:19,471 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:19,472 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:19,472 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:19,473 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:19,473 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:20,722 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:20,752 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:48:20,772 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:48:51,906 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:51,907 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:51,907 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:51,908 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:51,908 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:52,329 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 15:48:52,769 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 15:48:52,772 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 15:48:52,959 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 15:48:52,965 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 15:48:52,968 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 15:48:52,971 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 15:48:52,973 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 15:48:52,975 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 15:48:52,999 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:53,035 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 15:48:53,036 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 15:48:53,038 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:48:53,788 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:48:53,789 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:48:53,790 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:48:54,896 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:48:54,940 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:48:54,972 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:48:57,503 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 15:48:57,520 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,534 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,538 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,547 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,585 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,643 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,653 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,676 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,691 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,691 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,709 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,737 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 15:48:57,783 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:48:57,815 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:57] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 15:48:58,269 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:48:58,285 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:48:58,385 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:48:58] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 15:49:02,884 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:49:02,885 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:02,885 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:03,429 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 15:49:03,430 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:03,430 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:49:03,432 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:49:03] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:49:03,950 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:49:03] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:49:25,403 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:49:25,404 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 15:49:35,085 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:49:35,272 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:49:35,273 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:49:35,273 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 305128 字节)
2025-05-28 15:49:35,285 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:49:35,313 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:49:35,548 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:49:35,844 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:49:36,844 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:49:36,845 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:49:36,846 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_154903.png
2025-05-28 15:52:02,033 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:52:02,035 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,037 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,698 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 15:52:02,698 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:02,699 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:52:02,702 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:52:02] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:52:03,214 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:52:03] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:52:15,692 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:52:15,692 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********?fromshare=blogdetail&sharetype=blogdetail&sharerId=*********&sharerefer=PC&sharesource=weixin_46066085&sharefrom=from_link
2025-05-28 15:52:23,525 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:52:23,666 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:52:23,666 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:52:23,667 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 309689 字节)
2025-05-28 15:52:23,676 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:52:23,699 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:52:23,879 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:52:24,099 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:52:25,079 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:52:25,080 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:52:25,080 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155202.png
2025-05-28 15:53:08,460 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 15:53:08,461 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:08,461 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:09,717 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos设置用户名密码
2025-05-28 15:53:09,717 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:09,717 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 15:53:09,721 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:09] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 15:53:10,228 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:10] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 15:53:25,640 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 15:53:25,640 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/weixin_43966996/article/details/*********?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-*********-blog-*********.235^v43^pc_blog_bottom_relevance_base9&spm=1001.2101.3001.4242.2&utm_relevant_index=4
2025-05-28 15:53:34,272 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:34,432 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:34,433 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 15:53:34,433 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 70081 字节)
2025-05-28 15:53:34,438 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 15:53:34,461 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 15:53:34,692 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 15:53:34,918 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 15:53:35,734 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 15:53:35,735 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 15:53:35,736 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_155309.png
2025-05-28 15:53:59,035 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 15:53:59] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 15:55:10,742 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:55:11,272 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:55:12,145 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:55:12,146 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:55:12,146 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:55:12,147 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:55:12,147 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:55:13,423 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:55:13,446 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:55:13,462 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:05,193 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:05,487 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:06,123 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:06,124 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:06,124 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:07,204 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:07,223 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:07,235 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:27,599 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:28,081 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:28,747 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:28,748 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:28,749 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:29,818 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:29,852 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:29,868 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 15:56:48,155 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\utils\\screenshot.py', reloading
2025-05-28 15:56:48,431 - werkzeug - INFO -  * Restarting with stat
2025-05-28 15:56:49,149 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 15:56:49,150 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 15:56:49,151 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 15:56:49,151 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 15:56:49,151 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 15:56:50,204 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 15:56:50,222 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 15:56:50,237 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 16:04:31,022 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 16:04:31,024 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 16:04:31,024 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 16:04:31,456 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 16:04:31,888 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 16:04:31,891 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 16:04:32,060 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 16:04:32,065 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 16:04:32,068 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 16:04:32,071 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 16:04:32,074 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 16:04:32,076 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 16:04:32,095 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 16:04:32,138 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 16:04:32,139 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 16:04:32,141 - werkzeug - INFO -  * Restarting with stat
2025-05-28 16:04:32,950 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 16:04:32,954 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 16:04:32,954 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 16:04:32,955 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 16:04:32,955 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 16:04:35,248 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 16:04:35,288 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 16:04:35,312 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 16:04:35,553 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /?article_url=https://blog.csdn.net/fengbin2005/article/details/*********&email=<EMAIL> HTTP/1.1" 200 -
2025-05-28 16:04:35,716 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,718 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,725 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,786 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,800 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,820 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,859 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,863 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,906 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,921 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,931 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,940 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 16:04:35,963 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 16:04:35,978 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:35] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 16:04:36,454 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:04:36,461 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:04:36,520 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:36] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 16:04:42,252 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 16:04:42,253 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:42,254 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:43,141 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 16:04:43,141 - backend.api.csdn_api - INFO - 开始后台处理截图和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:04:43,141 - backend.api.csdn_api - INFO - 已启动后台任务处理截图和邮件发送
2025-05-28 16:04:43,147 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:43] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 16:04:43,671 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:04:43] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 16:04:57,671 - backend.utils.screenshot - WARNING - 提取文章信息失败: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:313:29)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-05-28 16:04:57,672 - backend.utils.screenshot - INFO - 开始截图: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 16:05:01,389 - backend.utils.screenshot - INFO - 找到CSDN文章内容区域
2025-05-28 16:05:08,506 - backend.utils.screenshot - INFO - 截图完成: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:05:08,774 - backend.api.csdn_api - INFO - 截图生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:05:08,774 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 16:05:08,775 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_截图.png (大小: 301689 字节)
2025-05-28 16:05:08,784 - backend.utils.mailer - INFO - 附件 CSDN文章_截图.png 添加成功
2025-05-28 16:05:08,819 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 16:05:09,033 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 16:05:09,275 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 16:05:10,495 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 16:05:10,497 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 16:05:10,498 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_160443.png
2025-05-28 16:09:37,028 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:09:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:14:37,023 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:14:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:19:37,029 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:19:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:24:37,019 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:24:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:29:37,035 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:29:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:34:37,017 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:34:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:40:08,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:40:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:45:08,049 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:45:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:50:08,022 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:50:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 16:55:08,018 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 16:55:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:00:08,017 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:00:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:08:30,837 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:08:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 17:09:37,022 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 17:09:37] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:23:55,615 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:23:55,617 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:23:55,617 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:23:56,080 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 18:23:56,815 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 18:23:56,821 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 18:23:57,074 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 18:23:57,080 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 18:23:57,088 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 18:23:57,091 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 18:23:57,094 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 18:23:57,097 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 18:23:57,129 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:23:57,226 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 18:23:57,226 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 18:23:57,228 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:23:58,176 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:23:58,177 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:23:58,178 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:23:58,178 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:23:58,178 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:23:59,640 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:23:59,677 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:23:59,712 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:24:02,704 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "GET / HTTP/1.1" 200 -
2025-05-28 18:24:02,990 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-28 18:24:02,992 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:02] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,005 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,025 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,027 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,033 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,057 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,076 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 18:24:03,080 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,092 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,102 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,111 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 18:24:03,471 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:24:03,491 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:24:03,955 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:24:03,973 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:03] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:24:04,024 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:04] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 18:24:09,838 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:24:09,838 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:09,838 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,476 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 18:24:10,478 - backend.api.csdn_api - INFO - 开始后台处理HTML生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,478 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 18:24:10,481 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:10] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 18:24:10,482 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:24:10,484 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:24:10,984 - backend.spiders.csdn_spider - INFO - 成功解析CSDN文章: nacos 2.x开启登录的用户名和密码
2025-05-28 18:24:10,986 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_182410.html
2025-05-28 18:24:10,986 - backend.utils.mailer - INFO - 初始化QQ邮件服务: <EMAIL>
2025-05-28 18:24:10,987 - backend.utils.mailer - INFO - 正在添加附件: CSDN文章_完整版.html (大小: 5117 字节)
2025-05-28 18:24:10,987 - backend.utils.mailer - INFO - 附件 CSDN文章_完整版.html 添加成功
2025-05-28 18:24:11,014 - backend.utils.mailer - INFO - 正在连接到SMTP服务器: smtp.qq.com:465
2025-05-28 18:24:11,018 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:24:11] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 18:24:11,213 - backend.utils.mailer - INFO - 正在登录邮箱: <EMAIL>
2025-05-28 18:24:11,493 - backend.utils.mailer - INFO - 正在发送邮件到: <EMAIL>
2025-05-28 18:24:12,063 - backend.utils.mailer - ERROR - SMTP错误: (-1, b'\x00\x00\x00')
2025-05-28 18:24:12,064 - backend.api.csdn_api - ERROR - 邮件发送失败: <EMAIL>
2025-05-28 18:24:12,065 - backend.api.csdn_api - INFO - 已清理临时文件: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_182410.html
2025-05-28 18:29:31,170 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:29:31] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:32:54,368 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:32:55,796 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:32:58,909 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:32:58,911 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:32:58,912 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:32:58,912 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:32:58,913 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:01,388 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:01,416 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:01,451 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:15,887 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:16,307 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:20,216 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:20,224 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:20,224 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:20,228 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:20,233 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:22,635 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:22,663 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:22,693 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:45,058 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:45,367 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:46,450 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:46,451 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:46,452 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:47,961 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:47,986 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:48,003 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:33:56,194 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:33:56,540 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:33:57,326 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:33:57,327 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:33:57,327 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:33:58,649 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:33:58,681 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:33:58,710 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:14,123 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:34:14,498 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:34:15,714 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:34:15,715 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:34:15,715 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:34:18,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:34:18,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:34:18,644 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:41,052 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\spiders\\csdn_spider.py', reloading
2025-05-28 18:34:41,244 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:34:42,019 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:34:42,020 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:34:42,021 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:34:43,208 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:34:43,226 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:34:43,247 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:34:59,527 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:34:59,819 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:35:00,748 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:35:00,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:35:00,750 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:35:02,089 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:35:02,110 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:35:02,129 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:19,575 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:19,995 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:21,260 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:21,261 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:21,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:23,144 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:23,171 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:23,202 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:32,372 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:32,683 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:33,546 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:33,547 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:33,547 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:33,548 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:33,548 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:34,632 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:34,650 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:34,663 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:36:44,844 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:36:45,157 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:36:45,944 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:36:45,945 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:36:45,946 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:36:45,946 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:36:45,946 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:36:47,173 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:36:47,196 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:36:47,215 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:37:10,691 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:37:10,925 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:37:11,983 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:37:11,984 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:37:11,984 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:37:13,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:37:13,587 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:37:13,609 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:41:14,609 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:41:14,610 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:41:14,610 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:41:15,012 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-28 18:41:15,599 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-28 18:41:15,605 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-28 18:41:15,893 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-28 18:41:15,897 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-28 18:41:15,900 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-28 18:41:15,902 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-28 18:41:15,906 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-28 18:41:15,908 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-28 18:41:15,923 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:41:15,979 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-28 18:41:15,980 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-28 18:41:15,982 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:41:16,679 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:41:16,680 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:41:16,680 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:41:17,668 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:41:17,681 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:41:17,696 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:41:20,791 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:20] "GET / HTTP/1.1" 200 -
2025-05-28 18:41:21,054 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,058 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,096 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,100 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,107 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,124 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,131 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,136 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,154 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-28 18:41:21,167 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,180 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,181 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-28 18:41:21,514 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:41:21,528 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:21] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-28 18:41:22,005 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:41:22,013 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-28 18:41:22,068 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:22] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-28 18:41:31,306 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:41:31,307 - backend.api.csdn_api - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:31,307 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:32,521 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:41:35,633 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:41:39,916 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:41:39,916 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:41:39,917 - backend.api.csdn_api - INFO - 开始后台处理HTML文件生成和邮件发送: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:39,917 - backend.api.csdn_api - INFO - 已启动后台任务处理HTML生成和邮件发送
2025-05-28 18:41:39,917 - backend.spiders.base_spider - INFO - 初始化爬虫: CSDN爬虫
2025-05-28 18:41:39,920 - backend.spiders.csdn_spider - INFO - 开始解析CSDN文章: https://blog.csdn.net/fengbin2005/article/details/*********
2025-05-28 18:41:39,921 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:39] "POST /api/csdn/parse HTTP/1.1" 200 -
2025-05-28 18:41:40,444 - werkzeug - INFO - 127.0.0.1 - - [28/May/2025 18:41:40] "[31m[1mPOST /api/search/record HTTP/1.1[0m" 400 -
2025-05-28 18:41:43,812 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第1次)
2025-05-28 18:41:49,068 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第2次)
2025-05-28 18:41:54,139 - backend.spiders.csdn_spider - WARNING - 请求被拦截，尝试重试 (第3次)
2025-05-28 18:41:54,139 - backend.spiders.csdn_spider - ERROR - 网络请求失败: 达到最大重试次数
2025-05-28 18:41:54,151 - backend.api.csdn_api - INFO - HTML文件生成成功: C:\Users\<USER>\AppData\Local\Temp\csdn_article_20250528_184154.html
2025-05-28 18:41:54,151 - backend.api.csdn_api - ERROR - 后台处理失败: name '_send_file_email' is not defined
2025-05-28 18:41:54,153 - backend.api.csdn_api - ERROR - Traceback (most recent call last):
  File "D:\Program Files\VsCodeProject\SuperSpider\backend\api\csdn_api.py", line 379, in _process_screenshot_and_email
    _send_file_email(email, full_article_data, output_path, format_type)
    ^^^^^^^^^^^^^^^^
NameError: name '_send_file_email' is not defined

2025-05-28 18:43:57,595 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:43:58,218 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:43:59,797 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:43:59,807 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:43:59,808 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:43:59,815 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:43:59,815 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:44:02,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:44:02,260 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:44:02,295 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:44:20,671 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:44:20,949 - werkzeug - INFO -  * Restarting with stat
2025-05-28 18:44:21,967 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-28 18:44:21,968 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-28 18:44:21,968 - superspider - INFO - 定时任务调度器初始化成功
2025-05-28 18:44:23,531 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-28 18:44:23,559 - werkzeug - WARNING -  * Debugger is active!
2025-05-28 18:44:23,577 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-28 18:45:03,834 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\csdn_api.py', reloading
2025-05-28 18:45:07,342 - werkzeug - INFO -  * Restarting with stat
