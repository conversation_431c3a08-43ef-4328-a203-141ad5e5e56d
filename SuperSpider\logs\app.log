2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:50:45,859 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:45,860 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:46,353 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:47,063 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:47,069 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:47,103 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:47,116 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:47,121 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:47,124 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:47,127 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:47,132 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:47,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:47,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 20:50:47,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 20:50:47,233 - werkzeug - INFO -  * Restarting with stat
2025-05-27 20:50:48,289 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:48,291 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:48,801 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:49,431 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:49,436 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:49,462 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:49,474 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:49,489 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:49,500 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:49,504 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:49,510 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:49,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:49,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 20:50:49,657 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 20:51:42,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:42,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,647 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,675 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:42,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,713 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:42,714 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:45,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,262 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,274 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:45,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,394 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:45,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:45,571 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:46,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:56:46,479 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:56:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:01:46,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:01:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:02:08,756 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:09,363 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:10,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:10,400 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:10,935 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:11,615 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:11,619 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:11,640 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:11,649 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:11,654 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:11,669 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:11,672 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:11,679 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:11,685 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:11,729 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:11,788 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:11,823 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:25,063 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:25,243 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:26,004 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:26,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:26,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:26,413 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:26,861 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:26,865 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:26,881 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:26,934 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:26,943 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:26,957 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:26,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:26,965 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:26,969 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:26,995 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:27,016 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:27,029 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:50,371 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 21:02:50,545 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:51,261 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:51,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:51,669 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:52,131 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:52,135 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:52,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:52,192 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:52,206 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:52,211 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:52,215 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:52,218 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:52,223 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:52,246 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:52,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:52,286 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:06:22,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET / HTTP/1.1" 200 -
2025-05-27 21:06:22,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 21:06:22,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,747 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:22,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:23,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,266 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:06:24,207 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:24,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:29,712 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:06:30,362 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:06:34,567 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:06:34,569 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:34] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:06:43,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:43,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,419 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:08:04,841 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:05,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:06,115 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:06,116 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:06,117 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:06,573 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:07,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:07,155 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:07,219 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:07,228 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:07,244 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:07,255 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:07,259 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:07,263 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:07,268 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:07,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:07,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:07,357 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:08:18,583 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:18,946 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:19,684 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:19,686 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:20,063 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:20,461 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:20,464 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:20,475 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:20,482 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:20,487 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:20,499 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:20,503 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:20,505 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:20,507 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:20,527 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:20,546 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:20,558 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:11:24,341 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:11:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:16:24,265 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:16:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,008 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/auth/profile HTTP/1.1" 200 -
2025-05-27 21:19:55,778 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:19:55,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:19:55] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:05,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:05,518 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:20:11,609 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:20:11,654 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:20:11] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:21:24,255 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:21:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:26:24,309 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:26:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:31:24,244 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:31:24] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:04,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET / HTTP/1.1" 200 -
2025-05-27 21:32:04,680 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,710 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,774 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,788 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,808 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,816 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 21:32:04,827 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,973 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:32:04,991 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:04] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,001 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:32:05,497 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,509 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:32:05,610 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:05] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:32:06,547 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:06,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:06] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:10,762 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:32:11,323 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7492526822198988090
2025-05-27 21:32:15,800 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 如何寻得“真爱”？（下半部分） #哲学 #爱情#齐泽克#爱情观
2025-05-27 21:32:15,802 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:15] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:32:19,920 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:19,942 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:19] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:28,851 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:32:33,053 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:32:33,054 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:33] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:32:36,042 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:32:36,063 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:32:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:36:10,855 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:36:11,646 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:16,204 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:16,206 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:16,207 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:16,790 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:17,577 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:17,584 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:17,622 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:17,633 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:17,638 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:17,640 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:17,644 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:17,648 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:17,675 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:17,721 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:40:17,722 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:40:17,724 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:40:18,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:40:18,406 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:40:18,435 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:40:18,436 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:40:18,950 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:40:19,450 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:40:19,453 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:40:19,467 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:40:19,472 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:40:19,477 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:40:19,479 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:40:19,482 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:40:19,485 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:40:19,505 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:40:19,524 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:40:19,546 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:41:05,247 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:05,248 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:05,249 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:05,249 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:05,628 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:06,039 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:06,043 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:06,062 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:06,067 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:06,073 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:06,074 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:06,079 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:06,081 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:06,101 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:06,134 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:41:06,134 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:41:06,137 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:41:06,825 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:41:06,826 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:41:06,827 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:41:06,827 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:41:07,233 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:41:07,664 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:41:07,667 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:41:07,680 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:41:07,686 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:41:07,691 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:41:07,693 - superspider - ERROR - 注册管理员API路由失败: No module named 'backend.models.download'
2025-05-27 21:41:07,696 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:41:07,698 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:41:07,714 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:41:07,735 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:41:07,748 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:06,285 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:42:06] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:42:13,566 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:13,842 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:14,537 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:14,539 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:14,540 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:14,540 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:14,894 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:15,327 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:15,331 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:15,347 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:15,353 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:15,361 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:15,367 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:15,371 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:15,374 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:15,394 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:15,419 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:15,478 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:34,744 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:34,899 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:35,749 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:35,750 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:35,751 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:36,113 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:36,515 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:36,518 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:36,531 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:36,536 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:36,539 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:36,546 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:36,549 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:36,551 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:36,567 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:36,585 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:36,598 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:42:50,814 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:42:50,977 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:42:51,820 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:42:51,821 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:42:51,822 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:42:51,822 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:42:52,199 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:42:52,681 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:42:52,685 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:42:52,699 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:42:52,706 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:42:52,710 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:42:52,715 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:42:52,718 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:42:52,722 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:42:52,737 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:42:52,761 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:42:52,781 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:06,987 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\admin_api.py', reloading
2025-05-27 21:43:07,183 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:43:07,960 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:07,962 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:07,962 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:08,379 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:08,850 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:08,854 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:08,870 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:08,880 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:08,887 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:08,897 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:08,909 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:08,949 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:09,027 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:09,131 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:43:09,144 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:43:58,520 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:43:58,521 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:43:58,522 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:43:58,522 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:43:58,928 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:43:59,387 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:43:59,392 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:43:59,407 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:43:59,412 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:43:59,416 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:43:59,421 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:43:59,424 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:43:59,428 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:43:59,450 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:43:59,502 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:43:59,503 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:43:59,505 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:00,152 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:00,154 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:00,155 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:00,532 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:00,954 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:00,957 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:00,972 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:00,977 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:00,980 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:00,983 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:00,987 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:00,990 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:01,008 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:01,029 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:01,045 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:11,951 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:11,952 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:11,953 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:12,371 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:12,790 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:12,794 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:12,806 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:12,813 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:12,816 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:12,820 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:12,822 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:12,824 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:12,842 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:12,886 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:44:12,887 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:44:12,889 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:44:13,549 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:44:13,550 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:44:13,550 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:44:13,908 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:44:14,301 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:44:14,305 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:44:14,317 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:44:14,324 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:44:14,330 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:44:14,332 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:44:14,335 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:44:14,337 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:44:14,351 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:44:14,374 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:44:14,389 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:44:32,666 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "GET / HTTP/1.1" 200 -
2025-05-27 21:44:32,875 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,878 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,882 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,891 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,912 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,931 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,945 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,958 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,963 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,984 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,986 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:32,992 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:32] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:44:33,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,282 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:44:33,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,772 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:44:33,826 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:33] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:44:35,724 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:35,734 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:35] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:41,186 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:44:41,574 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:44:45,354 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:44:45,357 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:45] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:44:49,005 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:44:49,031 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:44:49] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:45:19,105 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:19,106 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:19,106 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:19,591 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:20,133 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:20,137 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:20,151 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:20,155 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:20,159 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:20,162 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:20,165 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:20,168 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:20,184 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:20,243 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:45:20,244 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:45:20,248 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:20,870 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:20,871 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:20,872 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:21,219 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:21,618 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:21,621 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:21,638 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:21,644 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:21,650 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:21,652 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:21,655 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:21,657 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:21,681 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:21,705 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:21,722 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:45:46,086 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\run.py', reloading
2025-05-27 21:45:46,288 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:45:47,043 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:45:47,044 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:45:47,045 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:45:47,045 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:45:47,499 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:45:48,079 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:45:48,084 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:45:48,102 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:45:48,110 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:45:48,116 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:45:48,121 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:45:48,125 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:45:48,128 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:45:48,152 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:45:48,178 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:45:48,191 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:08,937 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:08,938 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:08,939 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:08,939 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:09,433 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:47:09,931 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:47:09,934 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:47:09,949 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:47:09,954 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:47:09,959 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:47:09,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:47:09,964 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:47:09,966 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:47:09,985 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:10,042 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:47:10,043 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:47:10,046 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:47:10,756 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:47:10,758 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:47:10,759 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:47:10,759 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:47:11,812 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:47:11,830 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:47:11,848 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:47:29,325 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "GET / HTTP/1.1" 200 -
2025-05-27 21:47:29,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,651 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,655 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,670 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,716 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,739 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,741 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,751 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,756 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:29,773 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:29] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:47:30,145 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,162 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:47:30,627 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,672 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:47:30,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:47:30] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:37,800 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:37,801 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:37,802 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:38,331 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:52:38,955 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:52:38,959 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:52:38,978 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:52:38,985 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:52:38,989 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:52:38,992 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:52:38,999 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:52:39,001 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:52:39,079 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:39,093 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:52:39,094 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:52:39,098 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:52:39,907 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:52:39,908 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:52:39,911 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:52:39,912 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:52:40,810 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:52:40,825 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:52:40,840 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:52:46,837 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "GET / HTTP/1.1" 200 -
2025-05-27 21:52:46,933 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,934 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,938 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:52:46,964 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:46] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,004 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,015 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,056 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,067 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,068 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,070 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:52:47,071 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 21:52:47,304 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,364 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:52:47,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,809 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:52:47,950 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:47] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:52:49,810 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:52:50,435 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:52:54,725 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:52:54,789 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:52:54,793 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:52:54] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:05,594 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:53:09,315 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,316 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:53:09,341 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:53:09,346 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:09] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:53:14,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,269 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:53:14,484 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:53:14,496 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:53:14] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:08,605 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:08,606 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:08,607 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:08,607 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:09,071 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:54:09,695 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:54:09,701 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:54:09,723 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:54:09,731 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:54:09,739 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:54:09,746 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:54:09,752 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:54:09,758 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:54:09,790 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:09,855 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:54:09,856 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:54:09,858 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:54:10,551 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:54:10,552 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:54:10,553 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:54:10,553 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:54:11,403 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:54:11,422 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:54:11,440 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:54:14,245 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /?platform=&content_type=&status= HTTP/1.1" 200 -
2025-05-27 21:54:14,271 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,298 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,308 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,314 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,320 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,370 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,371 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,382 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,385 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,400 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,403 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,417 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:54:14,711 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:14,727 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:14] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:54:15,202 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,213 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:54:15,258 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:15] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,572 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,582 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:20,631 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:20,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:20] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:27,270 - backend.api.bilibili_api - INFO - 开始解析哔哩哔哩视频: https://www.bilibili.com/video/BV1uzAZeVEYA/?share_source=copy_web&vd_source=6b1a893bc1d403075a53930e9bd9ff7d
2025-05-27 21:54:27,270 - backend.spiders.base_spider - INFO - 初始化爬虫: 哔哩哔哩爬虫
2025-05-27 21:54:27,703 - backend.spiders.bilibili_spider - INFO - 获取到最终URL: https://www.bilibili.com/video/BV1uzAZeVEYA/
2025-05-27 21:54:28,095 - backend.spiders.bilibili_spider - INFO - 视频文件已存在: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,096 - backend.spiders.bilibili_spider - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - backend.api.bilibili_api - INFO - 成功解析视频信息: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,097 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/bilibili/parse HTTP/1.1" 200 -
2025-05-27 21:54:28,113 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[33mGET /u002F/u002Fi2.hdslb.com/u002Fbfs/u002Farchive/u002Fc16e09fb1a1a2d86149343e9df435132701cf836.jpg HTTP/1.1[0m" 404 -
2025-05-27 21:54:28,127 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,133 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:28,150 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 小程序怎么上架？什么是备案？个人开发者怎么选择服务器？
2025-05-27 21:54:28,153 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:54:28,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:28] "[35m[1mGET /media/videos/小程序怎么上架？什么是备案？个人开发者怎么选择服务器？.mp4 HTTP/1.1[0m" 206 -
2025-05-27 21:54:32,598 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:32,629 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:32,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:32] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:54:35,943 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:35] "DELETE /api/search/2 HTTP/1.1" 200 -
2025-05-27 21:54:36,264 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:54:36,296 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:54:36] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:18,718 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:18,750 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:18,751 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:18,754 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:18,755 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:19,888 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:58:21,135 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:58:21,140 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:58:21,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:58:21,188 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:58:21,194 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:58:21,198 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:58:21,203 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:58:21,208 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:58:21,233 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:21,293 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 21:58:21,294 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 21:58:21,297 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:58:23,012 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:58:23,014 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:58:23,014 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:58:24,418 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:58:24,441 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:58:24,511 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:58:30,505 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:32,178 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,179 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:32,235 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:32,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:32] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:34,990 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,000 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:34] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:35,064 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:35,103 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:35] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:44,324 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:58:44,680 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,682 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:58:44,704 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:58:44,706 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:44] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 21:58:46,910 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,959 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:46,997 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:46] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:47,023 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:47] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:58:50,586 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "DELETE /api/search/5 HTTP/1.1" 200 -
2025-05-27 21:58:50,905 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:58:50,955 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:58:50] "GET /api/search/stats HTTP/1.1" 200 -
2025-05-27 21:59:15,239 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:59:16,547 - backend.spiders.base_spider - INFO - 初始化爬虫: 快手爬虫
2025-05-27 21:59:16,896 - backend.spiders.kuaishou_spider - INFO - 成功解析快手视频信息: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,898 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/kuaishou/parse HTTP/1.1" 200 -
2025-05-27 21:59:16,918 - backend.api.search_api - INFO - 用户 yumu 创建搜索记录: 困困的美食时间《猪尾炖猪腰》
2025-05-27 21:59:16,919 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:59:16] "POST /api/search/record HTTP/1.1" 200 -
2025-05-27 22:01:25,789 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 22:01:26,190 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:26,994 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:26,995 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:26,996 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:28,182 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:28,206 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:28,221 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:01:48,540 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 22:01:48,902 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:01:49,829 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:01:49,830 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:01:49,830 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:01:50,709 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:01:50,731 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:01:50,751 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 22:04:15,354 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:04:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:09:15,294 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:09:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:14:15,247 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:14:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:19:15,248 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:19:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:24:15,267 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:24:15] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:30:08,254 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:30:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:35:08,250 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:35:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:40:08,235 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:40:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:45:08,237 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 22:45:08] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 22:48:09,267 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:09,269 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:09,270 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:09,270 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:09,826 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:10,880 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:10,884 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:10,912 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:10,919 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:10,922 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:10,925 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:10,930 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:10,933 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:10,958 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:10,999 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:10,999 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:11,002 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:16,330 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:16,331 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:16,332 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:16,332 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:16,692 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 22:48:17,275 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 22:48:17,280 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 22:48:17,299 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 22:48:17,309 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 22:48:17,314 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 22:48:17,317 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 22:48:17,320 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 22:48:17,325 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 22:48:17,348 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:17,398 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 22:48:17,399 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 22:48:17,401 - werkzeug - INFO -  * Restarting with stat
2025-05-27 22:48:18,035 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 22:48:18,036 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 22:48:18,037 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 22:48:18,037 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 22:48:18,913 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 22:48:18,929 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 22:48:18,949 - werkzeug - INFO -  * Debugger PIN: 382-211-785
