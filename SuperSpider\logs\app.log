2025-05-27 16:15:36,698 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:36,699 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:36,700 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:36,700 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:37,211 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:38,181 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:38,185 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:38,209 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:38,216 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:38,224 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:38,228 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:38,231 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:38,236 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:38,276 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:38,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 16:15:38,374 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 16:15:38,379 - werkzeug - INFO -  * Restarting with stat
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 16:15:39,210 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 16:15:39,212 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 16:15:39,693 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 16:15:40,161 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 16:15:40,164 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 16:15:40,179 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 16:15:40,193 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 16:15:40,198 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 16:15:40,203 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 16:15:40,211 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 16:15:40,214 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 16:15:40,257 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 16:15:40,277 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 16:15:40,298 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 16:15:50,421 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET / HTTP/1.1" 200 -
2025-05-27 16:15:50,643 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-05-27 16:15:50,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/user.css HTTP/1.1" 200 -
2025-05-27 16:15:50,688 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permissions.js HTTP/1.1" 200 -
2025-05-27 16:15:50,703 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/images/wechat-qrcode.jpg HTTP/1.1" 200 -
2025-05-27 16:15:50,768 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 16:15:50,790 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/validation.js HTTP/1.1" 200 -
2025-05-27 16:15:50,830 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 16:15:50,853 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 16:15:50,858 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 16:15:50,860 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/video-controls.js HTTP/1.1" 200 -
2025-05-27 16:15:50,867 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/permission-management.js HTTP/1.1" 200 -
2025-05-27 16:15:50,869 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:50] "GET /static/js/direct-auth.js HTTP/1.1" 200 -
2025-05-27 16:15:51,171 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,201 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 16:15:51,669 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 16:15:51,759 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 16:15:51] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:50:45,859 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:45,860 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:45,860 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:46,353 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:47,063 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:47,069 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:47,103 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:47,116 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:47,121 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:47,124 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:47,127 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:47,132 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:47,166 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:47,227 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-05-27 20:50:47,228 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-27 20:50:47,233 - werkzeug - INFO -  * Restarting with stat
2025-05-27 20:50:48,289 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 20:50:48,291 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 20:50:48,291 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 20:50:48,801 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 20:50:49,431 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 20:50:49,436 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 20:50:49,462 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 20:50:49,474 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 20:50:49,489 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 20:50:49,500 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 20:50:49,504 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 20:50:49,510 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 20:50:49,565 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 20:50:49,620 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 20:50:49,657 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 20:51:42,483 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:42,611 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,628 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,647 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,659 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,675 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,681 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,683 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:42,692 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,713 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:42,714 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,718 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:42,725 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:42] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,143 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET / HTTP/1.1" 200 -
2025-05-27 20:51:45,197 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,204 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,261 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/css/downloads.css HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,262 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,273 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,274 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,347 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/auth.js HTTP/1.1" 200 -
2025-05-27 20:51:45,380 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/downloads.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,394 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-27 20:51:45,444 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,464 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,481 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 20:51:45,533 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:45,571 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:45] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 20:51:46,013 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,035 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 20:51:46,121 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:51:46] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 20:56:46,479 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 20:56:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:01:46,340 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:01:46] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:02:08,756 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:09,363 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:10,398 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:10,400 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:10,400 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:10,935 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:11,615 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:11,619 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:11,640 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:11,649 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:11,654 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:11,669 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:11,672 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:11,679 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:11,685 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:11,729 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:11,788 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:11,823 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:25,063 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\main.py', reloading
2025-05-27 21:02:25,243 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:26,004 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:26,005 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:26,006 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:26,006 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:26,413 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:26,861 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:26,865 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:26,881 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:26,934 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:26,943 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:26,957 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:26,961 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:26,965 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:26,969 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:26,995 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:27,016 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:27,029 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:02:50,371 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\models\\search_record.py', reloading
2025-05-27 21:02:50,545 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:02:51,261 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:02:51,262 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:02:51,262 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:02:51,669 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:02:52,131 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:02:52,135 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:02:52,181 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:02:52,192 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:02:52,206 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:02:52,211 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:02:52,215 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:02:52,218 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:02:52,223 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:02:52,246 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:02:52,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:02:52,286 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:06:22,473 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET / HTTP/1.1" 200 -
2025-05-27 21:06:22,557 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,558 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/css/user.css HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,561 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/css/downloads.css HTTP/1.1" 200 -
2025-05-27 21:06:22,574 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/images/wechat-qrcode.jpg HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,575 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permissions.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,607 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/validation.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,608 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,636 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /static/js/downloads.js HTTP/1.1" 200 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/video-controls.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,641 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/permission-management.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,644 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "[36mGET /static/js/direct-auth.js HTTP/1.1[0m" 304 -
2025-05-27 21:06:22,747 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:22,760 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:22] "GET /api/auth/check-auth HTTP/1.1" 200 -
2025-05-27 21:06:23,259 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,266 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/permission/check HTTP/1.1" 200 -
2025-05-27 21:06:23,311 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:23] "GET /api/activation/stats HTTP/1.1" 200 -
2025-05-27 21:06:24,207 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:24,226 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:24] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:29,712 - backend.spiders.base_spider - INFO - 初始化爬虫: 抖音爬虫
2025-05-27 21:06:30,362 - backend.spiders.douyin_spider - INFO - 获取到最终URL: https://www.douyin.com/video/7486704905982364962
2025-05-27 21:06:34,567 - backend.spiders.douyin_spider - INFO - 成功解析视频信息: 鲁大魔对谈珍妮教授，干货满满 #鲁大魔 #创业者 #博弈 #海鸥联盟
2025-05-27 21:06:34,569 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:34] "POST /api/douyin/parse HTTP/1.1" 200 -
2025-05-27 21:06:43,454 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:43,471 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:43] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,404 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:06:48,419 - werkzeug - INFO - 127.0.0.1 - - [27/May/2025 21:06:48] "GET /api/search/history?page=1&per_page=10&sort_field=created_at&sort_order=desc HTTP/1.1" 200 -
2025-05-27 21:08:04,841 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:05,263 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:06,115 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:06,116 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:06,117 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:06,117 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:06,573 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:07,149 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:07,155 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:07,219 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:07,228 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:07,244 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:07,255 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:07,259 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:07,263 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:07,268 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:07,300 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:07,334 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:07,357 - werkzeug - INFO -  * Debugger PIN: 382-211-785
2025-05-27 21:08:18,583 - werkzeug - INFO -  * Detected change in 'D:\\Program Files\\VsCodeProject\\SuperSpider\\backend\\api\\search_api.py', reloading
2025-05-27 21:08:18,946 - werkzeug - INFO -  * Restarting with stat
2025-05-27 21:08:19,684 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-05-27 21:08:19,685 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-05-27 21:08:19,686 - superspider - INFO - 定时任务调度器初始化成功
2025-05-27 21:08:20,063 - superspider - INFO - 已注册快手API路由到 /api/kuaishou
2025-05-27 21:08:20,461 - superspider - INFO - 已注册抖音API路由到 /api/douyin
2025-05-27 21:08:20,464 - superspider - INFO - 已注册哔哩哔哩API路由到 /api/bilibili
2025-05-27 21:08:20,475 - superspider - INFO - 已注册CSDN API路由到 /api/csdn
2025-05-27 21:08:20,482 - superspider - INFO - 已注册认证API路由到 /api/auth
2025-05-27 21:08:20,487 - superspider - INFO - 已注册下载历史API路由到 /api/download
2025-05-27 21:08:20,499 - superspider - INFO - 已注册搜索历史API路由到 /api/search
2025-05-27 21:08:20,503 - superspider - INFO - 已注册管理员API路由到 /api/admin
2025-05-27 21:08:20,505 - superspider - INFO - 已注册权限管理API路由到 /api/permission
2025-05-27 21:08:20,507 - superspider - INFO - 已注册激活码API路由到 /api/activation
2025-05-27 21:08:20,527 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-05-27 21:08:20,546 - werkzeug - WARNING -  * Debugger is active!
2025-05-27 21:08:20,558 - werkzeug - INFO -  * Debugger PIN: 382-211-785
