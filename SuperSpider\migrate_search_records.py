#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索记录数据库迁移脚本
添加唯一性约束并清理重复记录
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def migrate_search_records():
    """迁移搜索记录数据库"""
    db_path = os.path.join(current_dir, 'backend', 'database.db')
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，跳过迁移")
        return
    
    print("开始迁移搜索记录数据库...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 检查是否已经有唯一性约束
        cursor.execute("PRAGMA table_info(search_records)")
        columns = cursor.fetchall()
        print(f"当前表结构: {len(columns)} 列")
        
        # 2. 查找并删除重复记录，保留最新的
        print("查找重复记录...")
        cursor.execute("""
            SELECT user_id, content_url, COUNT(*) as count
            FROM search_records 
            GROUP BY user_id, content_url 
            HAVING COUNT(*) > 1
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"发现 {len(duplicates)} 组重复记录")
            
            for user_id, content_url, count in duplicates:
                print(f"处理用户 {user_id} 的重复记录: {content_url} ({count} 条)")
                
                # 获取所有重复记录，按创建时间排序
                cursor.execute("""
                    SELECT id, created_at, search_count 
                    FROM search_records 
                    WHERE user_id = ? AND content_url = ?
                    ORDER BY created_at DESC
                """, (user_id, content_url))
                
                records = cursor.fetchall()
                
                if len(records) > 1:
                    # 保留最新的记录，合并搜索次数
                    keep_id = records[0][0]
                    total_search_count = sum(record[2] for record in records)
                    
                    # 更新保留记录的搜索次数
                    cursor.execute("""
                        UPDATE search_records 
                        SET search_count = ?, last_searched_at = ?
                        WHERE id = ?
                    """, (total_search_count, datetime.now(), keep_id))
                    
                    # 删除其他重复记录
                    delete_ids = [record[0] for record in records[1:]]
                    for delete_id in delete_ids:
                        cursor.execute("DELETE FROM search_records WHERE id = ?", (delete_id,))
                        print(f"  删除重复记录 ID: {delete_id}")
        else:
            print("没有发现重复记录")
        
        # 3. 创建新表结构（带唯一性约束）
        print("创建新表结构...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS search_records_new (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                platform VARCHAR(50) NOT NULL,
                content_type VARCHAR(50) NOT NULL,
                content_url VARCHAR(1024) NOT NULL,
                title VARCHAR(255),
                author VARCHAR(100),
                status VARCHAR(20) DEFAULT 'success' NOT NULL,
                created_at DATETIME NOT NULL,
                search_count INTEGER DEFAULT 1,
                last_searched_at DATETIME,
                thumbnail_url VARCHAR(1024),
                duration INTEGER,
                video_url VARCHAR(1024),
                resolution VARCHAR(20),
                is_favorite BOOLEAN DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE (user_id, content_url)
            )
        """)
        
        # 4. 复制数据到新表
        print("复制数据到新表...")
        cursor.execute("""
            INSERT INTO search_records_new 
            SELECT * FROM search_records
        """)
        
        # 5. 删除旧表，重命名新表
        print("替换表结构...")
        cursor.execute("DROP TABLE search_records")
        cursor.execute("ALTER TABLE search_records_new RENAME TO search_records")
        
        # 6. 创建索引
        print("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_search_records_user_id ON search_records (user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_search_records_platform ON search_records (platform)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_platform_created ON search_records (user_id, platform, created_at)")
        
        conn.commit()
        print("✅ 数据库迁移完成！")
        
        # 7. 验证结果
        cursor.execute("SELECT COUNT(*) FROM search_records")
        total_count = cursor.fetchone()[0]
        print(f"迁移后总记录数: {total_count}")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_search_records()
